# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Environment Variables
.env
.env.local
.env.production
.env.staging

# Database Files (lokale SQLite-Datenbanken)
*.db
*.sqlite
*.sqlite3
instance/
dsv_rankings*.db

# Logs
logs/
*.log
data/logs/

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Data files (außer Konfiguration)
data/raw/*
data/processed/*
data/archive/*
!data/raw/.gitkeep
!data/processed/.gitkeep
!data/archive/.gitkeep

# SSL Certificates
ssl/
*.pem
*.key
*.crt

# Backup files
*.bak
*.backup

# Jest coverage reports
coverage/
*.lcov

# Test files
test.db
*.test.db

# Docker
.dockerignore

# Render.com
.render/

# Secrets und Keys
secrets.json
*.secret

# CSV Exports
dsv_rankings.csv

# Build artifacts
dist/
build/

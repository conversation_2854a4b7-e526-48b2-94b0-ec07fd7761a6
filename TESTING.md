# Testing Guide - DSV Scraper Node.js

Umfassende Anleitung für das Testen des DSV Scraper Systems.

## 🧪 Test-Setup

### Voraussetzungen

- Node.js 18.0.0+
- npm 9.0.0+
- Jest Test Framework
- Supertest für API Tests

### Installation

```bash
npm install
```

### Test-Konfiguration

Die Test-Konfiguration befindet sich in:
- `jest.config.js` - Jest Hauptkonfiguration
- `tests/setup.js` - Test Setup und Mocks

## 🏃‍♂️ Tests ausführen

### Alle Tests

```bash
npm test
```

### Tests mit Coverage

```bash
npm test -- --coverage
```

### Spezifische Test-Suites

```bash
# Unit Tests
npm test -- --testPathPattern="tests/(config|utils|scrapers|scheduler)"

# Integration Tests
npm test -- --testPathPattern="tests/integration"

# Web Application Tests
npm test -- --testPathPattern="tests/web"

# Einzelne Test-Datei
npm test tests/config.test.js
```

### Watch Mode

```bash
npm run test:watch
```

### Test Script

```bash
# Vollständige Test-Suite mit Linting und Coverage
./scripts/test.sh

# Mit Performance Tests
./scripts/test.sh --performance
```

## 📁 Test-Struktur

```
tests/
├── setup.js                    # Test Setup und Mocks
├── config.test.js              # Konfiguration Tests
├── utils/                      # Utility Tests
│   ├── logger.test.js
│   ├── DatabaseManager.test.js
│   └── DataProcessor.test.js
├── scrapers/                   # Scraper Tests
│   ├── BaseScraper.test.js
│   └── DSVScraper.test.js
├── scheduler/                  # Scheduler Tests
│   └── CronJobs.test.js
├── web/                        # Web Application Tests
│   ├── app.test.js
│   └── routes/
│       └── api.test.js
└── integration/                # Integration Tests
    └── scraping.test.js
```

## 🔧 Test-Kategorien

### Unit Tests

Testen einzelne Module isoliert:

- **Config Tests**: Konfigurationsvalidierung und Environment-Handling
- **Logger Tests**: Logging-Funktionalität und Formatierung
- **Database Tests**: Datenbankoperationen und Fehlerbehandlung
- **Scraper Tests**: Scraping-Logik und Datenextraktion
- **Scheduler Tests**: Cron-Jobs und Zeitplanung

### Integration Tests

Testen Zusammenspiel zwischen Modulen:

- **Scraping Workflow**: Vollständiger Scraping-Prozess
- **Data Processing**: Datenverarbeitung und -speicherung
- **Error Handling**: Fehlerbehandlung über Module hinweg

### API Tests

Testen Web-Endpoints:

- **REST API**: Alle API-Endpoints
- **Error Responses**: Fehlerbehandlung
- **Validation**: Input-Validierung
- **Authentication**: Sicherheit (falls implementiert)

## 🎭 Mocking

### Externe Services

```javascript
// Axios HTTP Requests
global.mockAxios = {
    get: jest.fn(),
    post: jest.fn(),
    create: jest.fn(() => global.mockAxios)
};

// Puppeteer Browser
global.mockPuppeteer = {
    launch: jest.fn(),
    newPage: jest.fn(),
    close: jest.fn()
};

// Nodemailer
global.mockNodemailer = {
    createTransporter: jest.fn(() => ({
        sendMail: jest.fn().mockResolvedValue({ messageId: 'test' })
    }))
};
```

### Database

```javascript
// Prisma Client Mock
const mockPrismaClient = {
    $connect: jest.fn(),
    $disconnect: jest.fn(),
    event: { findMany: jest.fn() },
    ranking: { create: jest.fn() }
};
```

### Test Helpers

```javascript
// Mock Swimmer Result
const swimmer = testHelpers.createMockSwimmerResult({
    swimmerName: 'Test Swimmer',
    timeSeconds: 120.5
});

// Mock HTML Response
const html = testHelpers.createMockHTMLResponse();
```

## 📊 Coverage

### Coverage-Ziele

- **Lines**: 70%+
- **Functions**: 70%+
- **Branches**: 70%+
- **Statements**: 70%+

### Coverage-Report

```bash
# HTML Report
npm test -- --coverage --coverageReporters=html
open coverage/lcov-report/index.html

# Text Summary
npm test -- --coverage --coverageReporters=text-summary
```

### Coverage-Ausschlüsse

Ausgeschlossen von Coverage:
- Test-Dateien (`*.test.js`, `*.spec.js`)
- Statische Assets (`src/web/public/**`)
- Templates (`src/web/views/**`)

## 🐛 Debugging Tests

### Debug-Modus

```bash
# Node.js Debugger
node --inspect-brk node_modules/.bin/jest --runInBand

# VS Code Debug
# Verwende "Jest Debug" Launch-Konfiguration
```

### Verbose Output

```bash
npm test -- --verbose
```

### Einzelne Tests debuggen

```bash
# Nur einen Test ausführen
npm test -- --testNamePattern="should create config"

# Test-Datei mit Debug-Output
npm test tests/config.test.js -- --verbose
```

## 🔍 Test-Best-Practices

### Test-Struktur

```javascript
describe('Module Name', () => {
    let moduleInstance;
    
    beforeEach(() => {
        // Setup vor jedem Test
        moduleInstance = new Module();
    });
    
    afterEach(() => {
        // Cleanup nach jedem Test
        jest.clearAllMocks();
    });
    
    test('should do something specific', () => {
        // Arrange
        const input = 'test input';
        
        // Act
        const result = moduleInstance.method(input);
        
        // Assert
        expect(result).toBe('expected output');
    });
});
```

### Async Tests

```javascript
test('should handle async operations', async () => {
    const result = await asyncFunction();
    expect(result).toBeDefined();
});

test('should handle promises', () => {
    return expect(promiseFunction()).resolves.toBe('value');
});

test('should handle rejections', () => {
    return expect(promiseFunction()).rejects.toThrow('error');
});
```

### Mock-Strategien

```javascript
// Mock gesamtes Modul
jest.mock('module-name');

// Mock spezifische Funktionen
const mockFunction = jest.fn().mockReturnValue('mocked');

// Mock mit Implementation
const mockFunction = jest.fn().mockImplementation((input) => {
    return `processed: ${input}`;
});

// Mock für verschiedene Aufrufe
mockFunction
    .mockReturnValueOnce('first call')
    .mockReturnValueOnce('second call')
    .mockReturnValue('default');
```

## 🚨 Continuous Integration

### GitHub Actions

```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm test -- --coverage
      - run: npm run lint
```

### Pre-commit Hooks

```bash
# Installiere husky
npm install --save-dev husky

# Setup pre-commit hook
npx husky add .husky/pre-commit "npm test"
```

## 📈 Performance Testing

### Einfache Performance Tests

```javascript
test('should complete scraping within time limit', async () => {
    const startTime = Date.now();
    
    await scraper.scrapeEvent(config);
    
    const duration = Date.now() - startTime;
    expect(duration).toBeLessThan(5000); // 5 Sekunden
});
```

### Memory Leaks

```javascript
test('should not leak memory', async () => {
    const initialMemory = process.memoryUsage().heapUsed;
    
    // Führe Operation mehrfach aus
    for (let i = 0; i < 100; i++) {
        await operation();
    }
    
    // Force garbage collection
    if (global.gc) global.gc();
    
    const finalMemory = process.memoryUsage().heapUsed;
    const memoryIncrease = finalMemory - initialMemory;
    
    expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024); // 10MB
});
```

## 🔧 Troubleshooting

### Häufige Probleme

1. **ES Modules Fehler**
   ```bash
   # Lösung: Verwende .js Extensions in Imports
   import module from './module.js';
   ```

2. **Timeout Fehler**
   ```javascript
   // Erhöhe Timeout für langsame Tests
   jest.setTimeout(30000);
   ```

3. **Mock Probleme**
   ```javascript
   // Stelle sicher, dass Mocks vor Import definiert sind
   jest.mock('module', () => ({ default: mockImplementation }));
   ```

4. **Database Tests**
   ```javascript
   // Verwende separate Test-Datenbank
   process.env.DATABASE_URL = 'file:./test.db';
   ```

## 📚 Weitere Ressourcen

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [Supertest Documentation](https://github.com/visionmedia/supertest)
- [Testing Best Practices](https://github.com/goldbergyoni/javascript-testing-best-practices)
- [Node.js Testing Guide](https://nodejs.org/en/docs/guides/testing/)

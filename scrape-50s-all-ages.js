#!/usr/bin/env node

/**
 * Vollständiges Scraping für 50m Schmetterling, männlich
 * Alle Altersklassen, alle Regionen, beide Kurse
 */

import { DSVScraper } from './src/scrapers/DSVScraper.js';
import { DataProcessor } from './src/utils/DataProcessor.js';
import DatabaseManager from './src/utils/DatabaseManager.js';

async function scrape50SchwimmAllAges() {
    console.log('🏊‍♂️ VOLLSTÄNDIGES SCRAPING: 50m Schmetterling, männlich');
    console.log('========================================================');
    
    let scraper, dataProcessor, dbManager;
    
    try {
        // Komponenten initialisieren
        console.log('\n📦 Initialisiere Komponenten...');
        
        dbManager = new DatabaseManager();
        await dbManager.connect();
        console.log('  ✅ Datenbank verbunden');
        
        dataProcessor = new DataProcessor(dbManager);
        console.log('  ✅ Data Processor bereit');
        
        scraper = new DSVScraper('scrapers/config/events.json');
        await scraper.loadConfig();
        console.log('  ✅ DSV Scraper initialisiert');
        
        // Event-Basis-Konfiguration
        const baseEventConfig = {
            eventName: '50m Schmetterling',
            eventCode: '50S|GL',
            distance: 50,
            stroke: 'Schmetterling',
            gender: 'M',
            season: '2025'
        };
        
        console.log('\n📋 Event-Konfiguration:');
        console.log(`  Event: ${baseEventConfig.eventName} (${baseEventConfig.eventCode})`);
        console.log(`  Gender: ${baseEventConfig.gender} (männlich)`);
        console.log(`  Saison: ${baseEventConfig.season}`);
        console.log(`  Altersklassen: ALLE (ohne Filter)`);
        
        // Alle Regionen laden (alle sind aktiviert, da kein enabled-Feld)
        const regions = scraper.config.regions;
        console.log(`\n🌍 Scraping für ${regions.length} Regionen:`);
        regions.forEach(region => {
            console.log(`  - ${region.name} (ID: ${region.id})`);
        });
        
        // Beide Kurse scrapen
        const courses = [
            { code: 'L', name: 'Langbahn (50m)' },
            { code: 'S', name: 'Kurzbahn (25m)' }
        ];
        
        let totalResults = 0;
        let totalRegionsProcessed = 0;
        let totalErrors = 0;
        const allResults = [];
        const errorLog = [];
        const failedRegions = []; // Fehlgeschlagene Regionen für Retry
        
        for (const course of courses) {
            console.log(`\n🏊 SCRAPING ${course.name.toUpperCase()}`);
            console.log('='.repeat(50));
            
            for (const region of regions) {
                const config = {
                    ...baseEventConfig,
                    course: course.code,
                    regionId: region.id,
                    ageGroup: '-1|-1'  // Alle Altersklassen
                };
                
                console.log(`\n📍 Region: ${region.name} (${course.name})`);
                
                try {
                    const results = await scraper.scrapeEvent(config);
                    
                    if (results.length > 0) {
                        console.log(`  ✅ ${results.length} Rankings gefunden`);
                        
                        // Erste 3 Ergebnisse anzeigen
                        console.log('  🏆 Top 3:');
                        results.slice(0, 3).forEach((result, i) => {
                            console.log(`    ${i+1}. ${result.swimmerName} - ${result.time} (${result.club})`);
                        });
                        
                        // Ergebnisse zur Gesamtliste hinzufügen
                        results.forEach(result => {
                            allResults.push({
                                ...result,
                                region: region.name,
                                regionId: region.id,
                                course: course.name,
                                courseCode: course.code,
                                eventName: baseEventConfig.eventName,
                                eventCode: baseEventConfig.eventCode,
                                distance: baseEventConfig.distance,
                                stroke: baseEventConfig.stroke,
                                gender: baseEventConfig.gender,
                                season: baseEventConfig.season,
                                ageGroup: '-1|-1',
                                scrapedAt: new Date()
                            });
                        });
                        
                        totalResults += results.length;
                        totalRegionsProcessed++;
                        
                        // Daten sofort in Datenbank speichern (pro Region)
                        try {
                            const eventKey = `${baseEventConfig.eventCode}_${baseEventConfig.gender}_ALL_${baseEventConfig.season}_${region.id}_${course.code}`;
                            await dataProcessor.processEventResults(eventKey, results.map(result => ({
                                ...result,
                                eventName: baseEventConfig.eventName,
                                eventCode: baseEventConfig.eventCode,
                                distance: baseEventConfig.distance,
                                stroke: baseEventConfig.stroke,
                                gender: baseEventConfig.gender,
                                season: baseEventConfig.season,
                                ageGroup: '-1|-1',
                                regionId: region.id,
                                scrapedAt: new Date()
                            })));
                            console.log(`  💾 ${results.length} Datensätze in DB gespeichert`);
                        } catch (dbError) {
                            console.log(`  ⚠️  DB-Fehler: ${dbError.message}`);
                            errorLog.push({
                                region: region.name,
                                course: course.name,
                                type: 'database',
                                error: dbError.message
                            });
                        }
                        
                    } else {
                        console.log('  ⚠️  Keine Rankings gefunden');
                    }
                    
                    // Pause zwischen Anfragen (Rate Limiting)
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    
                } catch (error) {
                    console.log(`  ❌ Scraping-Fehler: ${error.message}`);
                    totalErrors++;
                    errorLog.push({
                        region: region.name,
                        course: course.name,
                        type: 'scraping',
                        error: error.message
                    });

                    // Bei Server-Fehlern (5xx) für Retry vormerken
                    if (error.response?.status >= 500 && error.response?.status < 600) {
                        failedRegions.push({
                            region,
                            course,
                            config,
                            error: error.message
                        });
                    }
                    
                    // Bei 500-Fehlern etwas länger warten
                    if (error.message.includes('500')) {
                        console.log('  ⏳ Warte 5 Sekunden nach 500-Fehler...');
                        await new Promise(resolve => setTimeout(resolve, 5000));
                    }
                }
            }
            
            console.log(`\n📊 ${course.name} abgeschlossen:`);
            const courseResults = allResults.filter(r => r.courseCode === course.code);
            console.log(`  Gesamt Rankings: ${courseResults.length}`);
            console.log(`  Erfolgreich: ${totalRegionsProcessed} Regionen`);
            console.log(`  Fehler: ${totalErrors}`);
        }

        // RETRY FEHLGESCHLAGENER REGIONEN
        if (failedRegions.length > 0) {
            console.log(`\n🔄 RETRY FEHLGESCHLAGENER REGIONEN (${failedRegions.length})`);
            console.log('='.repeat(50));

            for (const failed of failedRegions) {
                console.log(`\n📍 Retry: ${failed.region.name} (${failed.course.name})`);
                console.log(`   Ursprünglicher Fehler: ${failed.error}`);

                try {
                    // Längere Pause vor Retry
                    await new Promise(resolve => setTimeout(resolve, 5000));

                    const results = await scraper.scrapeEvent(failed.config);

                    if (results.length > 0) {
                        console.log(`  ✅ ${results.length} Rankings gefunden (Retry erfolgreich!)`);

                        // Daten sofort in Datenbank speichern
                        const eventKey = `${baseEventConfig.eventCode}_${baseEventConfig.gender}_ALL_${baseEventConfig.season}_${failed.region.id}_${failed.course.code}`;
                        await dataProcessor.processEventResults(eventKey, results.map(result => ({
                            ...result,
                            region: failed.region.name,
                            regionId: failed.region.id,
                            course: failed.course.name,
                            courseCode: failed.course.code,
                            eventName: baseEventConfig.eventName,
                            eventCode: baseEventConfig.eventCode,
                            distance: baseEventConfig.distance,
                            stroke: baseEventConfig.stroke,
                            gender: baseEventConfig.gender,
                            season: baseEventConfig.season,
                            ageGroup: '-1|-1',
                            scrapedAt: new Date()
                        })));

                        totalResults += results.length;
                        totalRegionsProcessed++;

                        // Zu Gesamtergebnissen hinzufügen
                        results.forEach(result => {
                            allResults.push({
                                ...result,
                                region: failed.region.name,
                                regionId: failed.region.id,
                                course: failed.course.name,
                                courseCode: failed.course.code,
                                eventName: baseEventConfig.eventName,
                                eventCode: baseEventConfig.eventCode,
                                distance: baseEventConfig.distance,
                                stroke: baseEventConfig.stroke,
                                gender: baseEventConfig.gender,
                                season: baseEventConfig.season,
                                ageGroup: '-1|-1',
                                scrapedAt: new Date()
                            });
                        });

                    } else {
                        console.log('  ⚠️  Retry: Keine Rankings gefunden');
                    }

                } catch (retryError) {
                    console.log(`  ❌ Retry fehlgeschlagen: ${retryError.message}`);
                }
            }
        }

        console.log('\n🎉 SCRAPING ABGESCHLOSSEN!');
        console.log('==========================');
        console.log(`📊 GESAMTSTATISTIK:`);
        console.log(`  Gesamt gefundene Rankings: ${totalResults}`);
        console.log(`  Eindeutige Schwimmer: ${new Set(allResults.map(r => r.swimmerName)).size}`);
        console.log(`  Erfolgreich verarbeitete Regionen: ${totalRegionsProcessed}`);
        console.log(`  Fehler: ${totalErrors}`);
        
        // Beste Zeiten pro Kurs
        const langbahnResults = allResults.filter(r => r.courseCode === 'L');
        const kurbbahnResults = allResults.filter(r => r.courseCode === 'S');
        
        if (langbahnResults.length > 0) {
            const bestLangbahn = langbahnResults.sort((a, b) => a.time.localeCompare(b.time))[0];
            console.log(`\n🥇 BESTE ZEIT LANGBAHN: ${bestLangbahn.swimmerName} - ${bestLangbahn.time}`);
            console.log(`   Verein: ${bestLangbahn.club}, Region: ${bestLangbahn.region}`);
        }
        
        if (kurbbahnResults.length > 0) {
            const bestKurzbahn = kurbbahnResults.sort((a, b) => a.time.localeCompare(b.time))[0];
            console.log(`🥇 BESTE ZEIT KURZBAHN: ${bestKurzbahn.swimmerName} - ${bestKurzbahn.time}`);
            console.log(`   Verein: ${bestKurzbahn.club}, Region: ${bestKurzbahn.region}`);
        }
        
        // Regionen mit den meisten Rankings
        const regionStats = {};
        allResults.forEach(result => {
            if (!regionStats[result.region]) {
                regionStats[result.region] = 0;
            }
            regionStats[result.region]++;
        });
        
        console.log('\n📈 TOP 10 REGIONEN (nach Rankings):');
        Object.entries(regionStats)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .forEach(([region, count], index) => {
                console.log(`  ${index + 1}. ${region}: ${count} Rankings`);
            });
        
        // Fehler-Zusammenfassung
        if (errorLog.length > 0) {
            console.log('\n⚠️  FEHLER-ZUSAMMENFASSUNG:');
            const scrapingErrors = errorLog.filter(e => e.type === 'scraping');
            const dbErrors = errorLog.filter(e => e.type === 'database');
            
            if (scrapingErrors.length > 0) {
                console.log(`  Scraping-Fehler: ${scrapingErrors.length}`);
                scrapingErrors.slice(0, 5).forEach(error => {
                    console.log(`    - ${error.region} (${error.course}): ${error.error}`);
                });
                if (scrapingErrors.length > 5) {
                    console.log(`    ... und ${scrapingErrors.length - 5} weitere`);
                }
            }
            
            if (dbErrors.length > 0) {
                console.log(`  Datenbank-Fehler: ${dbErrors.length}`);
                dbErrors.slice(0, 3).forEach(error => {
                    console.log(`    - ${error.region} (${error.course}): ${error.error}`);
                });
            }
        }
        
        console.log('\n🎯 SCRAPING FÜR 50M SCHMETTERLING MÄNNLICH ABGESCHLOSSEN!');
        
    } catch (error) {
        console.error('\n❌ KRITISCHER FEHLER:', error.message);
        console.error('Stack:', error.stack);
    } finally {
        // Cleanup
        if (dbManager) {
            await dbManager.disconnect();
            console.log('\n📦 Datenbankverbindung geschlossen');
        }
    }
}

// Scraping starten
console.log('🚀 Starte vollständiges 50m Schmetterling Scraping...');
scrape50SchwimmAllAges().catch(console.error);

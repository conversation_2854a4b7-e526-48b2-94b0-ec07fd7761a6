# DSV Scraper - Node.js Version - Environment Configuration

# Node.js Environment
NODE_ENV=development
PORT=3000

# Database Configuration
DATABASE_TYPE=postgresql
DATABASE_URL=postgresql://dsv_user:dsv_password@localhost:5432/dsv_rankings
SQLITE_URL=file:./dsv_rankings.db

# Express Configuration
SECRET_KEY=your-secret-key-here
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Scraping Configuration
SCRAPING_DELAY=1
MAX_RETRIES=3
USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
TIMEOUT=30000

# Scheduling Configuration
ENABLE_SCHEDULER=true
SCRAPE_TIME=06:00
SCRAPE_DAY=sunday
TIMEZONE=Europe/Berlin

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/dsv-scraper.log

# Monitoring Configuration
SENTRY_DSN=your-sentry-dsn-here

# Notification Configuration
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password
NOTIFICATION_EMAIL=<EMAIL>

# Slack Notifications (optional)
SLACK_WEBHOOK_URL=your-slack-webhook-url

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# Puppeteer Configuration
PUPPETEER_HEADLESS=true
PUPPETEER_ARGS=--no-sandbox,--disable-setuid-sandbox,--disable-dev-shm-usage

/*
  Warnings:

  - Added the required column `ageGroup` to the `events` table without a default value. This is not possible if the table is not empty.
  - Made the column `gender` on table `events` required. This step will fail if there are existing NULL values in that column.

*/
-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_events" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "distance" INTEGER NOT NULL,
    "stroke" TEXT NOT NULL,
    "course" TEXT NOT NULL,
    "gender" TEXT NOT NULL,
    "ageGroup" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);
INSERT INTO "new_events" ("code", "course", "createdAt", "distance", "gender", "id", "name", "stroke", "updatedAt") SELECT "code", "course", "createdAt", "distance", "gender", "id", "name", "stroke", "updatedAt" FROM "events";
DROP TABLE "events";
ALTER TABLE "new_events" RENAME TO "events";
CREATE UNIQUE INDEX "events_code_gender_ageGroup_key" ON "events"("code", "gender", "ageGroup");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;

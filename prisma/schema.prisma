// Prisma Schema für DSV Scraper
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// Events (Schwimmdisziplinen)
model Event {
  id          String   @id @default(cuid())
  name        String   @unique
  code        String   @unique
  distance    Int
  stroke      String
  course      String   // L = Langbahn, K = Kurzbahn
  gender      String?  // M = Männer, W = Frauen
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Beziehungen
  rankings    Ranking[]

  @@map("events")
}

// Regionen (Bundesländer)
model Region {
  id           Int      @id
  name         String   @unique
  abbreviation String   @unique
  enabled      Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  // Beziehungen
  rankings     Ranking[]
  
  @@map("regions")
}

// Rankings (Schwimmergebnisse)
model Ranking {
  id           String   @id @default(cuid())
  
  // Schwimmer-Informationen
  swimmerName  String
  birthYear    Int?
  club         String
  
  // Event-Informationen
  eventId      String
  event        Event    @relation(fields: [eventId], references: [id], onDelete: Cascade)
  
  // Region
  regionId     Int
  region       Region   @relation(fields: [regionId], references: [id], onDelete: Cascade)
  
  // Wettkampf-Details
  time         String   // Format: "MM:SS.ss" oder "SS.ss"
  timeSeconds  Float    // Zeit in Sekunden für Sortierung
  rank         Int
  ageGroup     String   // z.B. "2015"
  gender       String   // M oder W
  season       String   // z.B. "2024/2025"
  
  // Wettkampf-Informationen
  competition  String?
  date         DateTime?
  location     String?
  
  // Metadaten
  scrapedAt    DateTime @default(now())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  // Indizes für bessere Performance
  @@index([eventId, regionId, ageGroup, gender])
  @@index([timeSeconds])
  @@index([scrapedAt])
  @@index([season])
  
  @@map("rankings")
}

// Scrape Logs (Protokollierung der Scraping-Vorgänge)
model ScrapeLog {
  id            String   @id @default(cuid())
  
  // Scraping-Details
  eventName     String
  regionId      Int
  ageGroup      String
  gender        String
  season        String
  
  // Ergebnis
  status        String   // success, error, partial
  resultCount   Int      @default(0)
  errorMessage  String?
  
  // Timing
  startTime     DateTime
  endTime       DateTime?
  duration      Int?     // in Millisekunden
  
  // Metadaten
  userAgent     String?
  ipAddress     String?
  
  createdAt     DateTime @default(now())
  
  // Indizes
  @@index([status])
  @@index([startTime])
  @@index([eventName, regionId])
  
  @@map("scrape_logs")
}

// Scheduler Jobs (Geplante Aufgaben)
model SchedulerJob {
  id           String   @id @default(cuid())
  
  // Job-Details
  name         String   @unique
  description  String?
  cronPattern  String
  enabled      Boolean  @default(true)
  
  // Letzter Lauf
  lastRun      DateTime?
  lastStatus   String?  // success, error, running
  lastDuration Int?     // in Millisekunden
  lastError    String?
  
  // Nächster Lauf
  nextRun      DateTime?
  
  // Statistiken
  totalRuns    Int      @default(0)
  successRuns  Int      @default(0)
  errorRuns    Int      @default(0)
  
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  @@map("scheduler_jobs")
}

// Notifications (Benachrichtigungen)
model Notification {
  id          String   @id @default(cuid())
  
  // Benachrichtigung-Details
  type        String   // email, slack, webhook
  recipient   String
  subject     String
  message     String
  
  // Status
  status      String   // pending, sent, failed
  sentAt      DateTime?
  errorMessage String?
  retryCount  Int      @default(0)
  
  // Metadaten
  priority    String   @default("normal") // low, normal, high, urgent
  category    String?  // scraping, error, system
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Indizes
  @@index([status])
  @@index([type])
  @@index([createdAt])
  
  @@map("notifications")
}

// System Settings (Systemeinstellungen)
model Setting {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  type      String   @default("string") // string, number, boolean, json
  category  String   @default("general")
  
  description String?
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("settings")
}

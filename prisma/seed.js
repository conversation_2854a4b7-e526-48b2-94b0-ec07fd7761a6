#!/usr/bin/env node

/**
 * Prisma Database Seeding Script
 * Seeds the database with initial data (regions)
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
    console.log('🌱 Starting database seeding...');

    // Seed regions
    console.log('📍 Seeding regions...');
    
    const regions = [
        { id: 1, name: "Baden", abbreviation: "BAD" },
        { id: 2, name: "Bayern", abbreviation: "BAY" },
        { id: 3, name: "Berlin", abbreviation: "BER" },
        { id: 4, name: "Brandenburg", abbreviation: "BRA" },
        { id: 5, name: "Bremen", abbreviation: "BRE" },
        { id: 6, name: "Hamburg", abbreviation: "HAM" },
        { id: 7, name: "Hessen", abbreviation: "HES" },
        { id: 8, name: "Mecklenburg-Vorpommern", abbreviation: "MEC" },
        { id: 9, name: "Niedersachsen", abbreviation: "NDS" },
        { id: 10, name: "Nordrhein", abbreviation: "NRW" },
        { id: 11, name: "Rheinland-Pfalz", abbreviation: "RLP" },
        { id: 12, name: "Saarland", abbreviation: "SAA" },
        { id: 13, name: "Sachsen", abbreviation: "SAC" },
        { id: 14, name: "Sachsen-Anhalt", abbreviation: "SAH" },
        { id: 15, name: "Schleswig-Holstein", abbreviation: "SHO" },
        { id: 16, name: "Thüringen", abbreviation: "THÜ" },
        { id: 17, name: "Westfalen", abbreviation: "WES" },
        { id: 18, name: "Württemberg", abbreviation: "WÜR" }
    ];

    for (const region of regions) {
        await prisma.region.upsert({
            where: { id: region.id },
            update: region,
            create: region
        });
    }

    console.log(`✅ Seeded ${regions.length} regions`);

    console.log('🎉 Database seeding completed!');
}

main()
    .catch((e) => {
        console.error('❌ Seeding failed:', e);
        process.exit(1);
    })
    .finally(async () => {
        await prisma.$disconnect();
    });

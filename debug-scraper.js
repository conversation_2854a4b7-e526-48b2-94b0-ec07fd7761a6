#!/usr/bin/env node

/**
 * Debug Script für DSV Scraper
 * Testet einzelne Schritte des Scraping-Prozesses
 */

import axios from 'axios';
import * as cheerio from 'cheerio';

async function debugScraper() {
    console.log('🔍 Debug DSV Scraper');
    console.log('====================');
    
    try {
        // Test 1: Einfacher GET Request
        console.log('\n📡 Test 1: Einfacher GET Request...');
        const url = 'https://dsvdaten.dsv.de/Modules/Clubs/Index.aspx?StateID=1';
        
        const response = await axios.get(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'de-DE,de;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive'
            },
            timeout: 30000
        });
        
        console.log(`✅ Status: ${response.status}`);
        console.log(`✅ Content-Length: ${response.data.length}`);
        
        // Test 2: HTML Parsing
        console.log('\n🔍 Test 2: HTML Parsing...');
        const $ = cheerio.load(response.data);

        // Prüfe neue Elemente
        const eventDropdown = $('#ContentSection__eventDropDownList');
        const seasonDropdown = $('#ContentSection__seasonDropDownList');
        const regionsDropdown = $('#ContentSection__regionsDropDownList');
        const genderRadio = $('input[name="ctl00$ContentSection$_genderRadioButtonList"]');
        const courseRadio = $('input[name="ctl00$ContentSection$_courseRadioButtonList"]');
        const rankingsButton = $('#ContentSection__rankingsButton');

        console.log(`✅ Event Dropdown gefunden: ${eventDropdown.length > 0}`);
        console.log(`✅ Season Dropdown gefunden: ${seasonDropdown.length > 0}`);
        console.log(`✅ Regions Dropdown gefunden: ${regionsDropdown.length > 0}`);
        console.log(`✅ Gender Radio Buttons gefunden: ${genderRadio.length > 0} (${genderRadio.length} Optionen)`);
        console.log(`✅ Course Radio Buttons gefunden: ${courseRadio.length > 0} (${courseRadio.length} Optionen)`);
        console.log(`✅ Rankings Button gefunden: ${rankingsButton.length > 0}`);
        
        // Test 3: Versteckte Felder
        console.log('\n🔐 Test 3: Versteckte Felder...');
        const viewState = $('input[name="__VIEWSTATE"]').val();
        const viewStateGenerator = $('input[name="__VIEWSTATEGENERATOR"]').val();
        const eventValidation = $('input[name="__EVENTVALIDATION"]').val();
        
        console.log(`✅ __VIEWSTATE: ${viewState ? 'gefunden' : 'FEHLT'} (${viewState?.length || 0} Zeichen)`);
        console.log(`✅ __VIEWSTATEGENERATOR: ${viewStateGenerator ? 'gefunden' : 'FEHLT'}`);
        console.log(`✅ __EVENTVALIDATION: ${eventValidation ? 'gefunden' : 'FEHLT'} (${eventValidation?.length || 0} Zeichen)`);
        
        // Test 4: Event-Optionen
        console.log('\n📋 Test 4: Verfügbare Events...');
        const eventOptions = $('#ContentSection__eventDropDownList option');
        console.log(`✅ Anzahl Events: ${eventOptions.length}`);

        eventOptions.each((i, el) => {
            const value = $(el).val();
            const text = $(el).text().trim();
            if (value && value !== '') {
                console.log(`   - ${value}: ${text}`);
            }
        });

        // Test 5: Gender Radio Optionen
        console.log('\n👫 Test 5: Verfügbare Gender...');
        genderRadio.each((i, el) => {
            const value = $(el).val();
            const label = $(`label[for="${$(el).attr('id')}"]`).text();
            const checked = $(el).prop('checked');
            console.log(`   - ${value}: ${label} ${checked ? '(selected)' : ''}`);
        });

        // Test 6: Course Radio Optionen
        console.log('\n🏊 Test 6: Verfügbare Courses...');
        courseRadio.each((i, el) => {
            const value = $(el).val();
            const label = $(`label[for="${$(el).attr('id')}"]`).text();
            const checked = $(el).prop('checked');
            console.log(`   - ${value}: ${label} ${checked ? '(selected)' : ''}`);
        });
        
        // Test 7: Einfacher POST Request (nur Filter setzen)
        console.log('\n📤 Test 7: Einfacher POST Request...');

        const postData = new URLSearchParams({
            '__VIEWSTATE': viewState,
            '__VIEWSTATEGENERATOR': viewStateGenerator,
            '__EVENTVALIDATION': eventValidation,
            'ctl00$ContentSection$_regionsDropDownList': '1',
            'ctl00$ContentSection$_seasonDropDownList': '2025',
            'ctl00$ContentSection$_eventDropDownList': '50F|GL',
            'ctl00$ContentSection$_genderRadioButtonList': 'M',
            'ctl00$ContentSection$_courseRadioButtonList': 'S'
        });
        
        const postResponse = await axios.post(url, postData.toString(), {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'de-DE,de;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Referer': url,
                'Origin': 'https://dsvdaten.dsv.de'
            },
            timeout: 30000
        });
        
        console.log(`✅ POST Status: ${postResponse.status}`);
        console.log(`✅ POST Content-Length: ${postResponse.data.length}`);
        
        // Prüfe ob Rankings-Tabelle vorhanden ist
        const $post = cheerio.load(postResponse.data);
        const rankingsTable = $post('#ContentSection__rankingsGridView');
        const rankingsRows = $post('#ContentSection__rankingsGridView tr');
        
        console.log(`✅ Rankings Tabelle gefunden: ${rankingsTable.length > 0}`);
        console.log(`✅ Rankings Zeilen: ${rankingsRows.length}`);
        
        if (rankingsRows.length > 1) {
            console.log('\n🎉 SUCCESS: Rankings gefunden!');
            rankingsRows.slice(1, 4).each((i, row) => {
                const cells = $post(row).find('td');
                if (cells.length > 0) {
                    const rank = $post(cells[0]).text().trim();
                    const name = $post(cells[1]).text().trim();
                    const time = $post(cells[2]).text().trim();
                    console.log(`   ${rank}. ${name} - ${time}`);
                }
            });
        } else {
            console.log('\n⚠️  Keine Rankings gefunden - möglicherweise keine Daten für diese Kombination');
        }
        
    } catch (error) {
        console.error('\n❌ Fehler beim Debug:', error.message);
        if (error.response) {
            console.error(`   Status: ${error.response.status}`);
            console.error(`   Headers:`, error.response.headers);
        }
        console.error(`   Stack:`, error.stack);
    }
}

// Führe Debug aus
debugScraper().catch(console.error);

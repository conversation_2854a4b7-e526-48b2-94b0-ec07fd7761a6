/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>punkt für DSV Scraper Web Application - Node.js Version
 */
import { createApp, startServer } from './web/app.js';
import config from './config.js';
import logger from './utils/logger.js';

/**
 * Appwrite Function Entry Point
 */
export async function main(req, res) {
    try {
        // Erstelle Express App
        const app = createApp();
        
        // Simuliere Express Request/Response für Appwrite
        const mockReq = {
            method: req.method || 'GET',
            url: req.path || '/',
            path: req.path || '/',
            query: req.query || {},
            headers: req.headers || {},
            body: req.body || {},
            get: (header) => req.headers?.[header.toLowerCase()],
            ip: req.headers?.['x-forwarded-for'] || '127.0.0.1'
        };
        
        const mockRes = {
            statusCode: 200,
            headers: {},
            setHeader: function(name, value) {
                this.headers[name] = value;
                if (res.headers) {
                    res.headers[name] = value;
                }
            },
            status: function(code) {
                this.statusCode = code;
                return this;
            },
            json: function(data) {
                this.setHeader('Content-Type', 'application/json');
                const response = JSON.stringify(data);
                if (res.send) {
                    return res.send(response, this.statusCode);
                }
                return response;
            },
            send: function(data) {
                if (res.send) {
                    return res.send(data, this.statusCode);
                }
                return data;
            },
            render: function(template, data) {
                // Für Appwrite: Einfache HTML-Antwort
                const html = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>${data.title || 'DSV Scraper'}</title>
                    <meta charset="utf-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1">
                    <style>
                        body { font-family: Arial, sans-serif; margin: 40px; }
                        .container { max-width: 800px; margin: 0 auto; }
                        .error { color: red; }
                        .success { color: green; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>${data.title || 'DSV Scraper'}</h1>
                        <p>Template: ${template}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                </body>
                </html>`;
                
                this.setHeader('Content-Type', 'text/html');
                if (res.send) {
                    return res.send(html, this.statusCode);
                }
                return html;
            }
        };
        
        // Express App als Middleware verwenden
        return new Promise((resolve, reject) => {
            try {
                app(mockReq, mockRes, (error) => {
                    if (error) {
                        logger.error('❌ Express App Fehler', { error: error.message });
                        reject(error);
                    } else {
                        resolve(mockRes);
                    }
                });
            } catch (error) {
                logger.error('❌ Unerwarteter Fehler in main()', { error: error.message });
                reject(error);
            }
        });
        
    } catch (error) {
        logger.error('❌ Fehler in Appwrite Function', {
            error: error.message,
            stack: error.stack
        });
        
        if (res && res.send) {
            return res.send(JSON.stringify({
                error: 'Interner Serverfehler',
                message: error.message
            }), 500);
        }
        
        throw error;
    }
}

/**
 * Standalone Server (für lokale Entwicklung)
 */
async function startStandaloneServer() {
    try {
        logger.info('🚀 Starte DSV Scraper Server (Standalone)');
        
        const app = createApp();
        const server = startServer(app);
        
        return server;
        
    } catch (error) {
        logger.error('❌ Fehler beim Starten des Standalone Servers', {
            error: error.message,
            stack: error.stack
        });
        process.exit(1);
    }
}

// Prüfe ob das Skript direkt ausgeführt wird (nicht als Appwrite Function)
if (import.meta.url === `file://${process.argv[1]}`) {
    startStandaloneServer();
}

export default main;

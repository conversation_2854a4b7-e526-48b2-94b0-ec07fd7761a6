/**
 * DSV-spezifischer Scraper für Node.js
 */
import * as cheerio from 'cheerio';
import { BaseScraper, SwimmerResult } from './BaseScraper.js';
import { scraperLogger } from '../utils/logger.js';

export class DSVScraper extends BaseScraper {
    constructor(configPath) {
        super(configPath);
        this.baseUrl = 'https://dsvdaten.dsv.de/Modules/Clubs/Index.aspx';
    }
    
    /**
     * Verbandsname zurückgeben
     */
    getFederationName() {
        return 'Deutscher Schwimm-Verband (DSV)';
    }
    
    /**
     * Event scrapen
     */
    async scrapeEvent(config) {
        const baseUrl = this.config?.scraping_config?.base_url || this.baseUrl;

        try {
            // Schritt 1: Initiale GET-Anfrage
            const getUrl = `${baseUrl}?StateID=${config.regionId}`;
            scraperLogger.debug('🌐 GET Request', { url: getUrl });

            const getResponse = await this.makeRequest('GET', getUrl);
            const $ = cheerio.load(getResponse.data);

            // Prüfe ob die Seite korrekt geladen wurde
            if (!this._validatePageStructure($)) {
                scraperLogger.warn('⚠️ Unerwartete Seitenstruktur', {
                    region: config.regionId,
                    url: getUrl
                });
                return [];
            }

            // Versteckte Felder sammeln
            const hiddenFields = this._extractHiddenFields($);
            scraperLogger.debug('🔍 Versteckte Felder gefunden', {
                count: Object.keys(hiddenFields).length,
                hasViewState: !!hiddenFields['__VIEWSTATE'],
                hasEventValidation: !!hiddenFields['__EVENTVALIDATION']
            });

            // Schritt 2: Filter setzen (ohne Button-Click)
            const filterPayload = this._buildCompletePayload(hiddenFields, config, false);

            const postHeaders = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Referer': getUrl,
                'Origin': 'https://dsvdaten.dsv.de'
            };

            // Warte zwischen Requests
            await this.waitBetweenRequests();

            const filterResponse = await this.makeRequest('POST', getUrl, {
                data: new URLSearchParams(filterPayload).toString(),
                headers: postHeaders
            });

            const $filter = cheerio.load(filterResponse.data);

            // Schritt 3: Daten laden mit Button-Click
            const hiddenFields2 = this._extractHiddenFields($filter);
            const dataPayload = this._buildCompletePayload(hiddenFields2, config, true);

            // Warte zwischen Requests
            await this.waitBetweenRequests();

            const dataResponse = await this.makeRequest('POST', getUrl, {
                data: new URLSearchParams(dataPayload).toString(),
                headers: postHeaders
            });

            // Daten parsen
            return this._parseRankingsTable(dataResponse.data, config);

        } catch (error) {
            scraperLogger.error('❌ Fehler beim Scraping von Event', {
                event: config.eventName,
                region: config.regionId,
                ageGroup: config.ageGroup,
                gender: config.gender,
                error: error.message,
                stack: error.stack
            });
            return [];
        }
    }
    
    /**
     * Seitenstruktur validieren
     */
    _validatePageStructure($) {
        // Prüfe ob wichtige Elemente vorhanden sind (korrigierte Selektoren)
        const hasEventDropdown = $('#ContentSection__eventDropDownList').length > 0;
        const hasAgeGroupDropdown = $('#ContentSection__ageDropDownList').length > 0;
        const hasRankingsButton = $('#ContentSection__rankingsButton').length > 0;
        const hasTimerangeDropdown = $('#ContentSection__timerangeDropDownList').length > 0;

        scraperLogger.debug('🔍 Seitenstruktur-Validierung', {
            hasEventDropdown,
            hasAgeGroupDropdown,
            hasRankingsButton,
            hasTimerangeDropdown
        });

        return hasEventDropdown && hasAgeGroupDropdown && hasRankingsButton;
    }

    /**
     * Versteckte Formularfelder extrahieren
     */
    _extractHiddenFields($) {
        const hiddenFields = {};

        $('input[type="hidden"]').each((i, element) => {
            const name = $(element).attr('name');
            const value = $(element).attr('value') || '';
            if (name) {
                hiddenFields[name] = value;
            }
        });

        return hiddenFields;
    }
    
    /**
     * Vollständige Payload erstellen mit allen erforderlichen Feldern
     */
    _buildCompletePayload(hiddenFields, config, includeButton = false) {
        const payload = {
            // Versteckte ASP.NET Felder
            ...hiddenFields,

            // Dropdown-Felder (neue Struktur)
            'ctl00$ContentSection$_regionsDropDownList': config.regionId?.toString() || '1',
            'ctl00$ContentSection$_seasonDropDownList': config.season || '2025',
            'ctl00$ContentSection$_eventDropDownList': config.eventCode || '50F|GL',

            // Radio-Buttons (neue Struktur)
            'ctl00$ContentSection$_genderRadioButtonList': config.gender || 'M',
            'ctl00$ContentSection$_courseRadioButtonList': config.course === 'L' ? 'L' : 'S',

            // Hidden Tab (falls noch benötigt)
            'ctl00$ContentSection$hiddenTab': '#meets'
        };

        // Button nur hinzufügen wenn explizit angefordert
        if (includeButton) {
            payload['ctl00$ContentSection$_rankingsButton'] = 'Daten laden';
        }

        scraperLogger.debug('🔧 Vollständige Payload erstellt', {
            includeButton,
            fieldCount: Object.keys(payload).length,
            event: payload['ctl00$ContentSection$_eventDropDownList'],
            gender: payload['ctl00$ContentSection$_genderRadioButtonList'],
            course: payload['ctl00$ContentSection$_courseRadioButtonList'],
            region: payload['ctl00$ContentSection$_regionsDropDownList']
        });

        return payload;
    }

    /**
     * Filter-Payload erstellen (Legacy-Methode für Kompatibilität)
     */
    _buildFilterPayload(config) {
        return this._buildCompletePayload({}, config, false);
    }
    
    /**
     * Rankings-Tabelle parsen (korrigierte Selektoren)
     */
    _parseRankingsTable(html, config) {
        const $ = cheerio.load(html);
        const results = [];

        // Finde die Rankings-Tabelle im rankings Tab
        const table = $('#rankings .table-responsive table').first();

        if (table.length === 0) {
            scraperLogger.warn('⚠️ Keine Rankings-Tabelle gefunden', {
                event: config.eventName,
                region: config.regionId,
                tableSelector: '#rankings .table-responsive table'
            });

            // Debug: Schaue nach alternativen Tabellen
            const allTables = $('table');
            scraperLogger.debug('🔍 Verfügbare Tabellen', {
                count: allTables.length,
                tablesWithRows: allTables.filter((i, table) => $(table).find('tbody tr').length > 0).length
            });

            return results;
        }

        // Parse Tabellenzeilen aus tbody (überspringe thead)
        const rows = table.find('tbody tr');

        scraperLogger.debug('🔍 Tabelle gefunden', {
            totalRows: rows.length,
            hasHeader: table.find('thead').length > 0
        });

        rows.each((i, row) => {
            try {
                const result = this._parseTableRow($(row), config);
                if (result) {
                    results.push(result);
                }
            } catch (error) {
                scraperLogger.warn('⚠️ Fehler beim Parsen von Zeile', {
                    row: i + 1,
                    error: error.message
                });
            }
        });

        scraperLogger.info('✅ Ergebnisse geparst', {
            event: config.eventName,
            region: config.regionId,
            ageGroup: config.ageGroup,
            count: results.length
        });

        return results;
    }
    
    /**
     * Einzelne Tabellenzeile parsen (korrigierte Spaltenstruktur)
     */
    _parseTableRow($row, config) {
        const cols = $row.find('td');

        if (cols.length < 8) {
            scraperLogger.debug('⚠️ Zeile hat zu wenige Spalten', {
                expected: 8,
                actual: cols.length,
                html: $row.html()
            });
            return null; // Nicht genug Spalten
        }

        try {
            // Spalten extrahieren (neue Struktur: Platz, Schwimmer, JG, Verein, Zeit, Pkt, Ort, Datum)
            const rank = parseInt($row.find('td').eq(0).text().trim()) || 0;
            const swimmerName = this.normalizeSwimmerName($row.find('td').eq(1).text().trim());
            const birthYearText = $row.find('td').eq(2).text().trim();
            const club = this.normalizeClubName($row.find('td').eq(3).text().trim());
            const time = $row.find('td').eq(4).text().trim();
            const points = $row.find('td').eq(5).text().trim() || null; // Punkte
            const location = $row.find('td').eq(6).text().trim() || null;
            const dateText = $row.find('td').eq(7).text().trim() || null;

            // Validierung
            if (!swimmerName || !time || rank === 0) {
                scraperLogger.debug('⚠️ Unvollständige Zeile', {
                    rank,
                    swimmerName,
                    time,
                    club
                });
                return null;
            }

            // Zeit in Sekunden konvertieren
            const timeSeconds = this.parseTimeToSeconds(time);
            if (!timeSeconds) {
                scraperLogger.debug('⚠️ Zeit konnte nicht geparst werden', { time });
                return null;
            }

            // Geburtsjahr extrahieren
            const birthYear = this.extractBirthYear(birthYearText);

            // Datum parsen
            let date = null;
            if (dateText && dateText.match(/\d{2}\.\d{2}\.\d{4}/)) {
                const [day, month, year] = dateText.split('.');
                date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
            }

            return new SwimmerResult({
                swimmerName,
                birthYear,
                club,
                time,
                timeSeconds,
                rank,
                ageGroup: config.ageGroup,
                gender: config.gender,
                competition: null, // Nicht in dieser Struktur verfügbar
                date,
                location
            });

        } catch (error) {
            scraperLogger.warn('⚠️ Fehler beim Parsen der Tabellenzeile', {
                error: error.message,
                html: $row.html()
            });
            return null;
        }
    }
}

export default DSVScraper;

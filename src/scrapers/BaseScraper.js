/**
 * Basis-Scraper Klasse für DSV Scraper - Node.js Version
 */
import puppeteer from 'puppeteer';
import axios from 'axios';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import config from '../config.js';
import { scraperLogger, logScrapingStart, logScrapingSuccess, logScrapingError } from '../utils/logger.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Scraping-Konfiguration für ein Event
 */
export class ScrapingConfig {
    constructor({
        eventName,
        eventCode,
        distance,
        stroke,
        course,
        ageGroup = null,
        gender,
        season,
        timeRange = null,
        regionId
    }) {
        this.eventName = eventName;
        this.eventCode = eventCode;
        this.distance = distance;
        this.stroke = stroke;
        this.course = course;
        this.ageGroup = ageGroup;
        this.gender = gender;
        this.season = season;
        this.timeRange = timeRange;
        this.regionId = regionId;
    }
}

/**
 * Schwimmer-Ergebnis Datenstruktur
 */
export class SwimmerResult {
    constructor({
        swimmerName,
        birthYear,
        club,
        time,
        timeSeconds,
        rank,
        ageGroup,
        gender,
        competition = null,
        date = null,
        location = null
    }) {
        this.swimmerName = swimmerName;
        this.birthYear = birthYear;
        this.club = club;
        this.time = time;
        this.timeSeconds = timeSeconds;
        this.rank = rank;
        this.ageGroup = ageGroup;
        this.gender = gender;
        this.competition = competition;
        this.date = date;
        this.location = location;
    }
}

/**
 * Basis-Scraper Klasse
 */
export class BaseScraper {
    constructor(configPath) {
        this.configPath = configPath;
        this.config = null;
        this.browser = null;
        this.page = null;
        this.axiosInstance = null;

        // Circuit Breaker für Server-Fehler
        this.circuitBreaker = {
            failures: 0,
            lastFailureTime: null,
            state: 'CLOSED', // CLOSED, OPEN, HALF_OPEN
            threshold: config.CIRCUIT_BREAKER_THRESHOLD,
            timeout: config.CIRCUIT_BREAKER_TIMEOUT
        };

        this.setupAxios();
    }
    
    /**
     * Konfiguration laden
     */
    async loadConfig() {
        try {
            const configFile = await fs.readFile(this.configPath, 'utf8');
            this.config = JSON.parse(configFile);
            scraperLogger.info('📋 Scraper-Konfiguration geladen', {
                events: this.config.events?.length || 0,
                regions: this.config.regions?.length || 0
            });
        } catch (error) {
            scraperLogger.error('❌ Fehler beim Laden der Scraper-Konfiguration', {
                path: this.configPath,
                error: error.message
            });
            throw error;
        }
    }
    
    /**
     * Axios-Instanz konfigurieren
     */
    setupAxios() {
        this.axiosInstance = axios.create({
            timeout: config.TIMEOUT,
            headers: {
                'User-Agent': config.USER_AGENT,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'de-DE,de;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        });
        
        // Request Interceptor für Logging
        this.axiosInstance.interceptors.request.use(
            (config) => {
                scraperLogger.debug('🌐 HTTP Request', {
                    method: config.method?.toUpperCase(),
                    url: config.url,
                    headers: config.headers
                });
                return config;
            },
            (error) => {
                scraperLogger.error('❌ HTTP Request Error', { error: error.message });
                return Promise.reject(error);
            }
        );
        
        // Response Interceptor für Logging
        this.axiosInstance.interceptors.response.use(
            (response) => {
                scraperLogger.debug('✅ HTTP Response', {
                    status: response.status,
                    url: response.config.url,
                    size: response.data?.length || 0
                });
                return response;
            },
            (error) => {
                scraperLogger.error('❌ HTTP Response Error', {
                    status: error.response?.status,
                    url: error.config?.url,
                    error: error.message
                });
                return Promise.reject(error);
            }
        );
    }
    
    /**
     * Browser initialisieren
     */
    async initBrowser() {
        try {
            this.browser = await puppeteer.launch(config.getPuppeteerConfig());
            this.page = await this.browser.newPage();
            
            // User Agent setzen
            await this.page.setUserAgent(config.USER_AGENT);
            
            // Viewport setzen
            await this.page.setViewport({
                width: 1920,
                height: 1080
            });
            
            scraperLogger.info('🚀 Browser initialisiert');
        } catch (error) {
            scraperLogger.error('❌ Fehler beim Initialisieren des Browsers', {
                error: error.message
            });
            throw error;
        }
    }
    
    /**
     * Browser schließen
     */
    async closeBrowser() {
        try {
            if (this.page) {
                await this.page.close();
                this.page = null;
            }
            if (this.browser) {
                await this.browser.close();
                this.browser = null;
            }
            scraperLogger.info('🔒 Browser geschlossen');
        } catch (error) {
            scraperLogger.error('❌ Fehler beim Schließen des Browsers', {
                error: error.message
            });
        }
    }
    
    /**
     * HTTP-Request mit Retry-Logik
     */
    async makeRequest(method, url, options = {}) {
        // Circuit Breaker prüfen
        if (this.circuitBreaker.state === 'OPEN') {
            const timeSinceLastFailure = Date.now() - this.circuitBreaker.lastFailureTime;
            if (timeSinceLastFailure < this.circuitBreaker.timeout) {
                throw new Error(`Circuit Breaker OPEN: Warte noch ${Math.ceil((this.circuitBreaker.timeout - timeSinceLastFailure) / 1000)}s`);
            } else {
                this.circuitBreaker.state = 'HALF_OPEN';
                scraperLogger.info('🔄 Circuit Breaker: HALF_OPEN - Teste Verbindung');
            }
        }

        let lastError;
        const maxRetries = config.MAX_RETRIES;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const response = await this.axiosInstance({
                    method,
                    url,
                    ...options
                });

                if (attempt > 1) {
                    scraperLogger.info('✅ Request erfolgreich nach Wiederholung', {
                        attempt,
                        url
                    });
                }

                // Circuit Breaker: Erfolg registrieren
                if (this.circuitBreaker.state === 'HALF_OPEN') {
                    this.circuitBreaker.state = 'CLOSED';
                    this.circuitBreaker.failures = 0;
                    scraperLogger.info('✅ Circuit Breaker: CLOSED - Verbindung wiederhergestellt');
                }

                return response;
            } catch (error) {
                lastError = error;
                const isServerError = this.isServerError(error);

                scraperLogger.warn(`⚠️ Request fehlgeschlagen (Versuch ${attempt}/${maxRetries})`, {
                    url,
                    error: error.message,
                    status: error.response?.status,
                    isServerError
                });

                if (attempt < maxRetries) {
                    // Längere Wartezeiten bei Server-Fehlern
                    const baseDelay = isServerError ? 2000 : 1000;
                    const maxDelay = isServerError ? 30000 : 10000;
                    const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay);

                    scraperLogger.debug(`⏳ Warte ${delay}ms vor nächstem Versuch`, { attempt, delay });
                    await this.sleep(delay);
                }
            }
        }

        // Circuit Breaker: Fehler registrieren
        if (this.isServerError(lastError)) {
            this.circuitBreaker.failures++;
            this.circuitBreaker.lastFailureTime = Date.now();

            if (this.circuitBreaker.failures >= this.circuitBreaker.threshold) {
                this.circuitBreaker.state = 'OPEN';
                scraperLogger.warn(`🚨 Circuit Breaker: OPEN - Zu viele Server-Fehler (${this.circuitBreaker.failures})`);
            }
        }

        throw lastError;
    }
    
    /**
     * Warten zwischen Requests
     */
    async waitBetweenRequests() {
        const delay = config.SCRAPING_DELAY * 1000;
        await this.sleep(delay);
    }
    
    /**
     * Sleep-Funktion
     */
    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Prüft ob es sich um einen Server-Fehler handelt (5xx)
     */
    isServerError(error) {
        return error?.response?.status >= 500 && error?.response?.status < 600;
    }
    
    /**
     * Zeit in Sekunden konvertieren
     */
    parseTimeToSeconds(timeString) {
        if (!timeString || typeof timeString !== 'string') {
            return null;
        }
        
        // Entferne Leerzeichen und normalisiere
        const cleanTime = timeString.trim().replace(/[^\d:.,]/g, '');
        
        try {
            // Format: MM:SS.ss oder M:SS.ss
            if (cleanTime.includes(':')) {
                const [minutes, seconds] = cleanTime.split(':');
                return parseInt(minutes) * 60 + parseFloat(seconds.replace(',', '.'));
            }
            
            // Format: SS.ss
            return parseFloat(cleanTime.replace(',', '.'));
        } catch (error) {
            scraperLogger.warn('⚠️ Fehler beim Parsen der Zeit', {
                timeString,
                cleanTime,
                error: error.message
            });
            return null;
        }
    }
    
    /**
     * Geburtsjahr aus Text extrahieren
     */
    extractBirthYear(text) {
        if (!text) return null;
        
        // Suche nach 4-stelligen Zahlen zwischen 1990 und 2020
        const matches = text.match(/\b(19[9]\d|20[0-2]\d)\b/);
        return matches ? parseInt(matches[0]) : null;
    }
    
    /**
     * Schwimmername normalisieren
     */
    normalizeSwimmerName(name) {
        if (!name) return '';
        
        return name
            .trim()
            .replace(/\s+/g, ' ')
            .replace(/[^\w\s\-äöüÄÖÜß]/g, '')
            .split(' ')
            .map(part => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase())
            .join(' ');
    }
    
    /**
     * Vereinsname normalisieren
     */
    normalizeClubName(club) {
        if (!club) return '';
        
        return club
            .trim()
            .replace(/\s+/g, ' ')
            .replace(/^(SV|SC|SSV|TSV|TV|TuS|VfL|SG)\s+/i, '$1 ');
    }
    
    /**
     * Alle Events scrapen
     */
    async scrapeAllEvents() {
        if (!this.config) {
            await this.loadConfig();
        }
        
        const results = {};
        const events = this.config.events.filter(e => e.enabled); // Nur aktivierte Events
        const genders = this.config.genders.filter(g => g.enabled).map(g => g.code);
        const courses = this.config.courses.filter(c => c.enabled).map(c => c.code);
        const regions = this.config.regions.map(r => r.id);
        const season = this.config.seasons.find(s => s.enabled)?.year || '2025';

        // Berechne die tatsächliche Anzahl der Kombinationen
        const totalCombinations = events.length * genders.length * courses.length * regions.length;
        let currentCombination = 0;

        scraperLogger.info('🚀 Starte Scraping aller Events', {
            events: events.length,
            genders: genders.length,
            courses: courses.length,
            regions: regions.length,
            totalCombinations
        });
        
        for (const event of events) {
            for (const gender of genders) {
                for (const course of courses) {
                    for (const regionId of regions) {
                        currentCombination++;

                        const scrapingConfig = new ScrapingConfig({
                            eventName: event.name,
                            eventCode: event.code,
                            distance: event.distance,
                            stroke: event.stroke,
                            course: course,
                            gender,
                            season,
                            regionId
                        });

                        const key = `${event.name}_${gender}_${course}_${regionId}`;

                        try {
                            logScrapingStart(event.name, scrapingConfig);

                            const startTime = Date.now();
                            const eventResults = await this.scrapeEvent(scrapingConfig);
                            const duration = Date.now() - startTime;

                            results[key] = eventResults;
                            logScrapingSuccess(event.name, eventResults.length, duration);
                            
                            // Fortschritt loggen
                            if (currentCombination % 10 === 0 || currentCombination === totalCombinations) {
                                scraperLogger.info('📊 Scraping-Fortschritt', {
                                    completed: currentCombination,
                                    total: totalCombinations,
                                    percentage: Math.round((currentCombination / totalCombinations) * 100)
                                });
                            }
                            
                            await this.waitBetweenRequests();
                            
                        } catch (error) {
                            logScrapingError(event.name, error, scrapingConfig);
                            results[key] = [];
                        }
                    }
                }
            }
        }
        
        return results;
    }
    
    /**
     * Abstrakte Methode: Event scrapen
     * Muss von abgeleiteten Klassen implementiert werden
     */
    async scrapeEvent(scrapingConfig) {
        throw new Error('scrapeEvent() muss von der abgeleiteten Klasse implementiert werden');
    }
    
    /**
     * Abstrakte Methode: Verbandsname
     * Muss von abgeleiteten Klassen implementiert werden
     */
    getFederationName() {
        throw new Error('getFederationName() muss von der abgeleiteten Klasse implementiert werden');
    }
}

export default BaseScraper;

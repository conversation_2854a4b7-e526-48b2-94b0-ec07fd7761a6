{"events": [{"name": "50m <PERSON><PERSON><PERSON><PERSON>", "code": "50F|GL", "distance": 50, "stroke": "Freistil", "course": "L", "gender": "M"}, {"name": "100m <PERSON><PERSON><PERSON><PERSON>", "code": "100F|GL", "distance": 100, "stroke": "Freistil", "course": "L", "gender": "M"}, {"name": "50m <PERSON><PERSON>", "code": "50B|GL", "distance": 50, "stroke": "Brust", "course": "L", "gender": "M"}, {"name": "100m <PERSON><PERSON>", "code": "100B|GL", "distance": 100, "stroke": "Brust", "course": "L", "gender": "M"}, {"name": "50m <PERSON><PERSON><PERSON>", "code": "50R|GL", "distance": 50, "stroke": "<PERSON><PERSON><PERSON>", "course": "L", "gender": "M"}, {"name": "100m <PERSON><PERSON><PERSON>", "code": "100R|GL", "distance": 100, "stroke": "<PERSON><PERSON><PERSON>", "course": "L", "gender": "M"}, {"name": "50m Sc<PERSON><PERSON><PERSON><PERSON>", "code": "50S|GL", "distance": 50, "stroke": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "course": "L", "gender": "M"}, {"name": "100m Schmetterling Mä<PERSON>", "code": "100S|GL", "distance": 100, "stroke": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "course": "L", "gender": "M"}, {"name": "200m Lagen Männer", "code": "200L|GL", "distance": 200, "stroke": "Lagen", "course": "L", "gender": "M"}], "age_groups": [{"year": "2015|2015", "name": "10 Jahre - JG 2015"}], "genders": [{"code": "M", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": "W", "name": "<PERSON><PERSON><PERSON>"}], "courses": [{"code": "L", "name": "Langbahn (50m)"}, {"code": "K", "name": "Kurzbahn (25m)"}], "seasons": [{"year": "2025", "name": "Saison 2024/2025"}, {"year": "2024", "name": "Saison 2023/2024"}], "time_ranges": [{"code": "01.06.2024|31.05.2025", "name": "Saison 2024/2025", "start_date": "2024-06-01", "end_date": "2025-05-31"}], "regions": [{"id": 2, "name": "Bayern", "abbreviation": "BAY"}, {"id": 6, "name": "Hamburg", "abbreviation": "HAM"}, {"id": 10, "name": "Nordrhein", "abbreviation": "NRW"}, {"id": 11, "name": "Rheinland-Pfalz", "abbreviation": "RLP"}], "scraping_config": {"base_url": "https://dsvdaten.dsv.de/Modules/Clubs/Index.aspx", "delay_between_requests": 2, "max_retries": 3, "timeout": 30, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"}}
/**
 * Konfiguration für DSV Scraper - Node.js Version
 */
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Lade Environment-spezifische .env Datei
const envFile = process.env.NODE_ENV === 'production' ? '.env.appwrite' : '.env';
dotenv.config({ path: path.join(__dirname, '..', envFile) });

class Config {
    constructor() {
        this.NODE_ENV = process.env.NODE_ENV || 'development';
        this.PORT = parseInt(process.env.PORT) || 3000;
        
        // Database Configuration
        this.DATABASE_TYPE = process.env.DATABASE_TYPE || 'postgresql';
        this.DATABASE_URL = process.env.DATABASE_URL || 'postgresql://dsv_user:dsv_password@localhost:5432/dsv_rankings';
        this.SQLITE_URL = process.env.SQLITE_URL || 'file:./dsv_rankings.db';
        
        // Appwrite Configuration
        this.APPWRITE_ENDPOINT = process.env.APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1';
        this.APPWRITE_PROJECT_ID = process.env.APPWRITE_PROJECT_ID || 'dsv-rankings';
        this.APPWRITE_API_KEY = process.env.APPWRITE_API_KEY;
        this.APPWRITE_DATABASE_ID = process.env.APPWRITE_DATABASE_ID || 'dsv-database';
        
        // Express Configuration
        this.SECRET_KEY = process.env.SECRET_KEY || 'your-secret-key-here';
        this.CORS_ORIGINS = process.env.CORS_ORIGINS ? process.env.CORS_ORIGINS.split(',') : ['http://localhost:3000'];
        
        // Scraping Configuration
        this.SCRAPING_DELAY = parseInt(process.env.SCRAPING_DELAY) || 1;
        this.MAX_RETRIES = parseInt(process.env.MAX_RETRIES) || 3;
        this.USER_AGENT = process.env.USER_AGENT || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';
        this.TIMEOUT = parseInt(process.env.TIMEOUT) || 30000;
        
        // Scheduling Configuration
        this.ENABLE_SCHEDULER = process.env.ENABLE_SCHEDULER === 'true';
        this.SCRAPE_TIME = process.env.SCRAPE_TIME || '06:00';
        this.SCRAPE_DAY = process.env.SCRAPE_DAY || 'sunday';
        this.TIMEZONE = process.env.TIMEZONE || 'Europe/Berlin';
        
        // Logging Configuration
        this.LOG_LEVEL = process.env.LOG_LEVEL || 'info';
        this.LOG_FILE = process.env.LOG_FILE || 'logs/dsv-scraper.log';
        
        // Monitoring Configuration
        this.SENTRY_DSN = process.env.SENTRY_DSN;
        
        // Notification Configuration
        this.EMAIL_SMTP_SERVER = process.env.EMAIL_SMTP_SERVER;
        this.EMAIL_SMTP_PORT = parseInt(process.env.EMAIL_SMTP_PORT) || 587;
        this.EMAIL_USERNAME = process.env.EMAIL_USERNAME;
        this.EMAIL_PASSWORD = process.env.EMAIL_PASSWORD;
        this.NOTIFICATION_EMAIL = process.env.NOTIFICATION_EMAIL;
        this.SLACK_WEBHOOK_URL = process.env.SLACK_WEBHOOK_URL;
        
        // File Paths
        this.DATA_DIR = path.join(__dirname, '..', 'data');
        this.RAW_DATA_DIR = path.join(this.DATA_DIR, 'raw');
        this.PROCESSED_DATA_DIR = path.join(this.DATA_DIR, 'processed');
        this.ARCHIVE_DATA_DIR = path.join(this.DATA_DIR, 'archive');
        this.LOGS_DIR = path.join(__dirname, '..', 'logs');
        
        // Events Configuration
        this.EVENTS_CONFIG_PATH = path.join(__dirname, 'scrapers', 'config', 'events.json');
        
        // Rate Limiting
        this.RATE_LIMIT_WINDOW = parseInt(process.env.RATE_LIMIT_WINDOW) || 15 * 60 * 1000; // 15 minutes
        this.RATE_LIMIT_MAX = parseInt(process.env.RATE_LIMIT_MAX) || 100; // requests per window
        
        // Puppeteer Configuration
        this.PUPPETEER_HEADLESS = process.env.PUPPETEER_HEADLESS !== 'false';
        this.PUPPETEER_ARGS = process.env.PUPPETEER_ARGS ? process.env.PUPPETEER_ARGS.split(',') : [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--single-process',
            '--disable-gpu'
        ];
    }
    
    /**
     * Validiert die Konfiguration
     */
    validate() {
        const errors = [];
        
        if (this.DATABASE_TYPE === 'appwrite' && !this.APPWRITE_API_KEY) {
            errors.push('APPWRITE_API_KEY ist erforderlich wenn DATABASE_TYPE=appwrite');
        }
        
        if (this.ENABLE_SCHEDULER && !this.SCRAPE_TIME.match(/^\d{2}:\d{2}$/)) {
            errors.push('SCRAPE_TIME muss im Format HH:MM sein');
        }
        
        if (errors.length > 0) {
            throw new Error(`Konfigurationsfehler:\n${errors.join('\n')}`);
        }
    }
    
    /**
     * Gibt die Datenbankverbindungs-URL zurück
     */
    getDatabaseUrl() {
        if (this.DATABASE_TYPE === 'sqlite') {
            return this.SQLITE_URL;
        }
        return this.DATABASE_URL;
    }
    
    /**
     * Prüft ob Appwrite verwendet wird
     */
    isAppwriteEnabled() {
        return this.DATABASE_TYPE === 'appwrite' && this.APPWRITE_API_KEY;
    }
    
    /**
     * Gibt die Puppeteer-Konfiguration zurück
     */
    getPuppeteerConfig() {
        return {
            headless: this.PUPPETEER_HEADLESS,
            args: this.PUPPETEER_ARGS,
            defaultViewport: {
                width: 1920,
                height: 1080
            },
            timeout: this.TIMEOUT
        };
    }
}

// Singleton-Instanz
const config = new Config();
config.validate();

export default config;

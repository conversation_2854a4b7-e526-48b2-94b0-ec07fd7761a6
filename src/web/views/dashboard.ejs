<%- include('layouts/main', { 
    title: title || 'Dashboard',
    currentPage: 'dashboard',
    body: `
        <!-- Welcome Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="jumbotron bg-primary text-white p-4 rounded">
                    <h1 class="display-4">
                        <i class="bi bi-trophy"></i> DSV Rankings Dashboard
                    </h1>
                    <p class="lead">Willkommen zum DSV Schwimm-Rankings System - Node.js Version</p>
                    <hr class="my-4" style="border-color: rgba(255,255,255,0.3);">
                    <p>Automatisiertes Scraping und Darstellung von DSV-Schwimmdaten mit moderner Node.js Architektur.</p>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4>${statistics.totalRankings || 0}</h4>
                                <p class="mb-0">Rankings</p>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-list-ol fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4>${statistics.totalEvents || 0}</h4>
                                <p class="mb-0">Events</p>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-calendar-event fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4>${statistics.totalRegions || 0}</h4>
                                <p class="mb-0">Regionen</p>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-geo fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card bg-secondary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4>${statistics.recentScrapeLogs || 0}</h4>
                                <p class="mb-0">Scrape Logs</p>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-file-text fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scraping Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-play-circle"></i> Scraping Aktionen
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <button onclick="startManualScraping()" class="btn btn-primary w-100" id="manualScrapeBtn">
                                    <i class="bi bi-play-fill"></i> Manuelles Scraping starten
                                </button>
                                <small class="text-muted">Startet Scraping für alle aktivierten Events</small>
                            </div>
                            <div class="col-md-4 mb-3">
                                <button onclick="showEventScrapeModal()" class="btn btn-outline-primary w-100">
                                    <i class="bi bi-target"></i> Einzelnes Event scrapen
                                </button>
                                <small class="text-muted">Scraping für spezifisches Event</small>
                            </div>
                            <div class="col-md-4 mb-3">
                                <a href="/api/scrape/status" class="btn btn-outline-info w-100" target="_blank">
                                    <i class="bi bi-activity"></i> Scraping Status
                                </a>
                                <small class="text-muted">Aktueller Status und Logs</small>
                            </div>
                        </div>

                        <!-- Scraping Status Display -->
                        <div id="scrapingStatus" class="mt-3" style="display: none;">
                            <div class="alert alert-info">
                                <div class="d-flex align-items-center">
                                    <div class="spinner-border spinner-border-sm me-2" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <div>
                                        <strong>Scraping läuft...</strong>
                                        <div id="scrapingMessage">Initialisierung...</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Scraping Results -->
                        <div id="scrapingResults" class="mt-3" style="display: none;">
                            <div class="alert alert-success">
                                <h6><i class="bi bi-check-circle"></i> Scraping abgeschlossen!</h6>
                                <div id="resultsContent"></div>
                                <div class="mt-2">
                                    <a href="/rankings" class="btn btn-sm btn-success me-2">
                                        <i class="bi bi-list-ol"></i> Rankings anzeigen
                                    </a>
                                    <button onclick="location.reload()" class="btn btn-sm btn-outline-secondary">
                                        <i class="bi bi-arrow-clockwise"></i> Dashboard aktualisieren
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-lightning"></i> Schnellzugriff
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a href="/rankings" class="btn btn-outline-primary w-100">
                                    <i class="bi bi-list-ol"></i> Rankings anzeigen
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="/events" class="btn btn-outline-secondary w-100">
                                    <i class="bi bi-calendar-event"></i> Events verwalten
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="/api/export/csv" class="btn btn-outline-success w-100">
                                    <i class="bi bi-download"></i> CSV Export
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="/health" class="btn btn-outline-info w-100" target="_blank">
                                    <i class="bi bi-heart-pulse"></i> System Health
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-gear"></i> System-Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li><strong>Version:</strong> Node.js DSV Scraper v1.0.0</li>
                            <li><strong>Framework:</strong> Express.js + Prisma</li>
                            <li><strong>Datenbank:</strong> SQLite (Development)</li>
                            <li><strong>Letztes Update:</strong> ${statistics.lastUpdated ? new Date(statistics.lastUpdated).toLocaleString('de-DE') : 'Unbekannt'}</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-info-circle"></i> Nützliche Links
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li><a href="/api/rankings" class="text-decoration-none">REST API</a></li>
                            <li><a href="/statistics" class="text-decoration-none">Detaillierte Statistiken</a></li>
                            <li><a href="/logs" class="text-decoration-none">System-Logs</a></li>
                            <li><a href="https://github.com/your-repo/dsv-scraper" class="text-decoration-none" target="_blank">GitHub Repository</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Event Scraping Modal -->
        <div class="modal fade" id="eventScrapeModal" tabindex="-1" aria-labelledby="eventScrapeModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="eventScrapeModalLabel">
                            <i class="bi bi-target"></i> Einzelnes Event scrapen
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="eventScrapeForm">
                            <div class="mb-3">
                                <label for="eventName" class="form-label">Event Name</label>
                                <select class="form-select" id="eventName" required>
                                    <option value="">Event auswählen...</option>
                                    <option value="200m Lagen">200m Lagen</option>
                                    <option value="100m Freistil">100m Freistil</option>
                                    <option value="50m Freistil">50m Freistil</option>
                                    <option value="100m Brust">100m Brust</option>
                                    <option value="100m Rücken">100m Rücken</option>
                                    <option value="100m Schmetterling">100m Schmetterling</option>
                                </select>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="ageGroup" class="form-label">Jahrgang</label>
                                        <select class="form-select" id="ageGroup" required>
                                            <option value="">Jahrgang auswählen...</option>
                                            <option value="2015">2015</option>
                                            <option value="2014">2014</option>
                                            <option value="2013">2013</option>
                                            <option value="2012">2012</option>
                                            <option value="2011">2011</option>
                                            <option value="2010">2010</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="gender" class="form-label">Geschlecht</label>
                                        <select class="form-select" id="gender" required>
                                            <option value="">Geschlecht auswählen...</option>
                                            <option value="M">Männlich</option>
                                            <option value="W">Weiblich</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="regionId" class="form-label">Region (optional)</label>
                                <select class="form-select" id="regionId">
                                    <option value="">Alle Regionen</option>
                                    <option value="1">Region 1 - Schleswig-Holstein</option>
                                    <option value="2">Region 2 - Hamburg</option>
                                    <option value="3">Region 3 - Niedersachsen</option>
                                    <option value="4">Region 4 - Bremen</option>
                                    <option value="5">Region 5 - Nordrhein-Westfalen</option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Abbrechen</button>
                        <button type="button" class="btn btn-primary" onclick="startEventScraping()">
                            <i class="bi bi-play-fill"></i> Scraping starten
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // Manual Scraping Functions
            async function startManualScraping() {
                const btn = document.getElementById('manualScrapeBtn');
                const statusDiv = document.getElementById('scrapingStatus');
                const resultsDiv = document.getElementById('scrapingResults');
                const messageDiv = document.getElementById('scrapingMessage');

                // Reset UI
                resultsDiv.style.display = 'none';
                statusDiv.style.display = 'block';
                btn.disabled = true;
                btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Läuft...';
                messageDiv.textContent = 'Starte manuelles Scraping...';

                try {
                    const response = await fetch('/api/scrape/manual', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    const result = await response.json();

                    if (result.success) {
                        messageDiv.textContent = 'Scraping gestartet! Job ID: ' + result.jobId;

                        // Poll for completion (simplified)
                        setTimeout(() => {
                            statusDiv.style.display = 'none';
                            resultsDiv.style.display = 'block';
                            document.getElementById('resultsContent').innerHTML =
                                '<p>Manuelles Scraping wurde gestartet. Überprüfen Sie die <a href="/logs">Logs</a> für Details.</p>';
                        }, 3000);
                    } else {
                        throw new Error(result.details || 'Unbekannter Fehler');
                    }

                } catch (error) {
                    statusDiv.style.display = 'none';
                    alert('Fehler beim Starten des Scrapings: ' + error.message);
                } finally {
                    btn.disabled = false;
                    btn.innerHTML = '<i class="bi bi-play-fill"></i> Manuelles Scraping starten';
                }
            }

            function showEventScrapeModal() {
                const modal = new bootstrap.Modal(document.getElementById('eventScrapeModal'));
                modal.show();
            }

            async function startEventScraping() {
                const form = document.getElementById('eventScrapeForm');
                const formData = new FormData(form);

                const eventData = {
                    eventName: document.getElementById('eventName').value,
                    ageGroup: document.getElementById('ageGroup').value,
                    gender: document.getElementById('gender').value,
                    regionId: document.getElementById('regionId').value || undefined
                };

                if (!eventData.eventName || !eventData.ageGroup || !eventData.gender) {
                    alert('Bitte füllen Sie alle erforderlichen Felder aus.');
                    return;
                }

                try {
                    const response = await fetch('/api/scrape/event', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(eventData)
                    });

                    const result = await response.json();

                    if (result.success) {
                        // Close modal
                        const modal = bootstrap.Modal.getInstance(document.getElementById('eventScrapeModal'));
                        modal.hide();

                        // Show results
                        const resultsDiv = document.getElementById('scrapingResults');
                        resultsDiv.style.display = 'block';
                        document.getElementById('resultsContent').innerHTML =
                            '<p><strong>Event:</strong> ' + result.event + '</p>' +
                            '<p><strong>Jahrgang:</strong> ' + result.ageGroup + ' | <strong>Geschlecht:</strong> ' + result.gender + '</p>' +
                            '<p><strong>Gefunden:</strong> ' + result.rankingsFound + ' Rankings | <strong>Gespeichert:</strong> ' + result.rankingsSaved + '</p>';
                    } else {
                        throw new Error(result.details || 'Unbekannter Fehler');
                    }

                } catch (error) {
                    alert('Fehler beim Event-Scraping: ' + error.message);
                }
            }
        </script>
    `
}) %>

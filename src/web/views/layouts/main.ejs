<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - DSV Rankings</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-trophy"></i> DSV Rankings
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <%= currentPage === 'dashboard' ? 'active' : '' %>" href="/">
                            <i class="bi bi-house"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <%= currentPage === 'rankings' ? 'active' : '' %>" href="/rankings">
                            <i class="bi bi-list-ol"></i> Rankings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <%= currentPage === 'events' ? 'active' : '' %>" href="/events">
                            <i class="bi bi-calendar-event"></i> Events
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <%= currentPage === 'regions' ? 'active' : '' %>" href="/regions">
                            <i class="bi bi-geo"></i> Regionen
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <%= currentPage === 'statistics' ? 'active' : '' %>" href="/statistics">
                            <i class="bi bi-graph-up"></i> Statistiken
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <%= currentPage === 'logs' ? 'active' : '' %>" href="/logs">
                            <i class="bi bi-file-text"></i> Logs
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="scrapingDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-play-circle"></i> Scraping
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="DSVRankings.startScraping('manual')">
                                <i class="bi bi-play-fill"></i> Manuelles Scraping
                            </a></li>
                            <li><a class="dropdown-item" href="/api/scrape/status" target="_blank">
                                <i class="bi bi-activity"></i> Scraping Status
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logs">
                                <i class="bi bi-file-text"></i> Scraping Logs
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/health" target="_blank">
                            <i class="bi bi-heart-pulse"></i> Health
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mt-4">
        <%- body %>
    </main>

    <!-- Footer -->
    <footer class="bg-light mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 DSV Rankings - Node.js Version</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="/api/rankings" class="text-decoration-none me-3">API</a>
                    <a href="/api/export/csv" class="text-decoration-none">CSV Export</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
</body>
</html>

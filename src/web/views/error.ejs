<%- include('layouts/main', { 
    title: title || '<PERSON><PERSON>',
    currentPage: 'error',
    body: `
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-exclamation-triangle"></i>
                            ${error.status || 500} - ${title || '<PERSON><PERSON>'}
                        </h4>
                    </div>
                    <div class="card-body">
                        <p class="card-text">${error.message || 'Ein unerwarteter Fehler ist aufgetreten.'}</p>
                        
                        ${error.status === 404 ? `
                            <div class="mt-3">
                                <h6>Mögliche Lösungen:</h6>
                                <ul>
                                    <li>Überprüfen Sie die URL auf Tippfehler</li>
                                    <li>Kehren Sie zur <a href="/">Startseite</a> zurück</li>
                                    <li>Verwenden Sie die Navigation oben</li>
                                </ul>
                            </div>
                        ` : ''}
                        
                        <div class="mt-4">
                            <a href="/" class="btn btn-primary">
                                <i class="bi bi-house"></i> Zur Startseite
                            </a>
                            <button onclick="history.back()" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Zurück
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4 text-center">
                    <small class="text-muted">
                        Wenn das Problem weiterhin besteht, überprüfen Sie die 
                        <a href="/logs">System-Logs</a> oder den 
                        <a href="/health">Health-Status</a>.
                    </small>
                </div>
            </div>
        </div>
    `
}) %>

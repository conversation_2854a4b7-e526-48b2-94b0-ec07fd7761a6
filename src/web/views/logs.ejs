<%- include('layouts/main', { 
    title: title || 'System Logs',
    currentPage: 'logs',
    body: `
        <!-- <PERSON> Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="bi bi-file-text"></i> ${title || 'System Logs'}</h2>
                <p class="text-muted">System-Logs und Scraping-Protokolle</p>
            </div>
        </div>

        <!-- Log Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-funnel"></i> Log-Filter</h6>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="/logs">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="level" class="form-label">Log Level</label>
                                    <select class="form-select" id="level" name="level">
                                        <option value="">Alle Level</option>
                                        <option value="error" ${filters.level === 'error' ? 'selected' : ''}>Error</option>
                                        <option value="warn" ${filters.level === 'warn' ? 'selected' : ''}>Warning</option>
                                        <option value="info" ${filters.level === 'info' ? 'selected' : ''}>Info</option>
                                        <option value="debug" ${filters.level === 'debug' ? 'selected' : ''}>Debug</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-3 mb-3">
                                    <label for="category" class="form-label">Kategorie</label>
                                    <select class="form-select" id="category" name="category">
                                        <option value="">Alle Kategorien</option>
                                        <option value="scraping" ${filters.category === 'scraping' ? 'selected' : ''}>Scraping</option>
                                        <option value="database" ${filters.category === 'database' ? 'selected' : ''}>Database</option>
                                        <option value="web" ${filters.category === 'web' ? 'selected' : ''}>Web</option>
                                        <option value="scheduler" ${filters.category === 'scheduler' ? 'selected' : ''}>Scheduler</option>
                                        <option value="system" ${filters.category === 'system' ? 'selected' : ''}>System</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-3 mb-3">
                                    <label for="limit" class="form-label">Anzahl</label>
                                    <select class="form-select" id="limit" name="limit">
                                        <option value="50" ${pagination.limit === 50 ? 'selected' : ''}>50</option>
                                        <option value="100" ${pagination.limit === 100 ? 'selected' : ''}>100</option>
                                        <option value="200" ${pagination.limit === 200 ? 'selected' : ''}>200</option>
                                        <option value="500" ${pagination.limit === 500 ? 'selected' : ''}>500</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-search"></i> Filtern
                                        </button>
                                        <a href="/logs" class="btn btn-outline-secondary">
                                            <i class="bi bi-x-circle"></i> Reset
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scrape Logs -->
        ${scrapeLogs && scrapeLogs.length > 0 ? `
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-clock-history"></i> Scraping-Logs</h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover mb-0">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Zeitstempel</th>
                                            <th>Event</th>
                                            <th>Region</th>
                                            <th>Status</th>
                                            <th>Ergebnisse</th>
                                            <th>Dauer</th>
                                            <th>Fehler</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${scrapeLogs.map(log => `
                                            <tr>
                                                <td><small>${new Date(log.startTime).toLocaleString('de-DE')}</small></td>
                                                <td>${log.eventName}</td>
                                                <td>Region ${log.regionId}</td>
                                                <td>
                                                    <span class="badge ${log.status === 'success' ? 'bg-success' : log.status === 'error' ? 'bg-danger' : 'bg-warning'}">
                                                        ${log.status}
                                                    </span>
                                                </td>
                                                <td>${log.resultCount || 0}</td>
                                                <td>${log.duration ? Math.round(log.duration / 1000) + 's' : '-'}</td>
                                                <td>
                                                    ${log.errorMessage ? `
                                                        <small class="text-danger" title="${log.errorMessage}">
                                                            ${log.errorMessage.substring(0, 50)}${log.errorMessage.length > 50 ? '...' : ''}
                                                        </small>
                                                    ` : '-'}
                                                </td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        ` : ''}

        <!-- System Logs (Simulated) -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="bi bi-terminal"></i> System-Logs</h6>
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshLogs()">
                            <i class="bi bi-arrow-clockwise"></i> Aktualisieren
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="systemLogs" style="height: 400px; overflow-y: auto; background-color: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px;">
                            <div class="log-entry text-success">[${new Date().toISOString()}] INFO: DSV Scraper Server gestartet auf Port ${process.env.PORT || 3000}</div>
                            <div class="log-entry text-info">[${new Date(Date.now() - 60000).toISOString()}] INFO: Datenbank-Verbindung erfolgreich</div>
                            <div class="log-entry text-primary">[${new Date(Date.now() - 120000).toISOString()}] INFO: Express-Server initialisiert</div>
                            <div class="log-entry text-secondary">[${new Date(Date.now() - 180000).toISOString()}] DEBUG: Prisma Client generiert</div>
                            <div class="log-entry text-success">[${new Date(Date.now() - 240000).toISOString()}] INFO: Alle Module erfolgreich geladen</div>
                            <div class="log-entry text-warning">[${new Date(Date.now() - 300000).toISOString()}] WARN: Scheduler deaktiviert (ENABLE_SCHEDULER=false)</div>
                            <div class="log-entry text-info">[${new Date(Date.now() - 360000).toISOString()}] INFO: Winston Logger konfiguriert</div>
                            <div class="log-entry text-secondary">[${new Date(Date.now() - 420000).toISOString()}] DEBUG: Environment-Variablen geladen</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Log Statistics -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-graph-up"></i> Log-Statistiken</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center">
                                    <h4 class="text-success">${scrapeLogs ? scrapeLogs.filter(l => l.status === 'success').length : 0}</h4>
                                    <small class="text-muted">Erfolgreiche Scrapes</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <h4 class="text-danger">${scrapeLogs ? scrapeLogs.filter(l => l.status === 'error').length : 0}</h4>
                                    <small class="text-muted">Fehlgeschlagene Scrapes</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-info-circle"></i> Log-Information</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li><strong>Log-Datei:</strong> <code>${process.env.LOG_FILE || 'logs/dsv-scraper.log'}</code></li>
                            <li><strong>Log-Level:</strong> <code>${process.env.LOG_LEVEL || 'info'}</code></li>
                            <li><strong>Rotation:</strong> Täglich</li>
                            <li><strong>Aufbewahrung:</strong> 30 Tage</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <script>
            function refreshLogs() {
                // Simulate log refresh
                const logsContainer = document.getElementById('systemLogs');
                const newLogEntry = document.createElement('div');
                newLogEntry.className = 'log-entry text-info';
                newLogEntry.textContent = '[' + new Date().toISOString() + '] INFO: Logs aktualisiert';
                logsContainer.insertBefore(newLogEntry, logsContainer.firstChild);
                
                // Scroll to top
                logsContainer.scrollTop = 0;
            }
            
            // Auto-refresh every 30 seconds
            setInterval(refreshLogs, 30000);
        </script>
    `
}) %>

<%- include('layouts/main', { 
    title: title || 'Regionen',
    currentPage: 'regions',
    body: `
        <!-- <PERSON>er -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="bi bi-geo"></i> ${title || 'Regionen'}</h2>
                <p class="text-muted">DSV Schwimmregionen und deren Statistiken</p>
            </div>
        </div>

        <!-- Regions Grid -->
        <div class="row">
            ${regions.length > 0 ? regions.map(region => `
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 ${region.enabled ? '' : 'border-secondary'}">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">${region.name}</h6>
                            <span class="badge ${region.enabled ? 'bg-success' : 'bg-secondary'}">
                                ${region.enabled ? 'Aktiv' : 'Inaktiv'}
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">Region ID:</small>
                                    <div><strong>${region.id}</strong></div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">Kürzel:</small>
                                    <div><code>${region.abbreviation}</code></div>
                                </div>
                            </div>
                            
                            <div class="row mt-3">
                                <div class="col-12">
                                    <small class="text-muted">Rankings:</small>
                                    <div class="d-flex align-items-center">
                                        <span class="fs-4 text-primary me-2">${region.rankingCount || 0}</span>
                                        <small class="text-muted">Einträge</small>
                                    </div>
                                </div>
                            </div>
                            
                            ${region.createdAt ? `
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <small class="text-muted">Erstellt:</small>
                                        <div><small>${new Date(region.createdAt).toLocaleDateString('de-DE')}</small></div>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                        <div class="card-footer">
                            <div class="btn-group w-100" role="group">
                                <a href="/rankings?regionId=${region.id}" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-list-ol"></i> Rankings
                                </a>
                                <a href="/api/export/csv?regionId=${region.id}" class="btn btn-outline-success btn-sm">
                                    <i class="bi bi-download"></i> CSV
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('') : `
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="bi bi-geo-alt-fill fs-1 text-muted"></i>
                            <h5 class="mt-3 text-muted">Keine Regionen verfügbar</h5>
                            <p class="text-muted">
                                Es sind noch keine Regionen konfiguriert. 
                                Regionen werden automatisch beim ersten Scraping erstellt.
                            </p>
                            <a href="/api/regions" class="btn btn-outline-primary">
                                <i class="bi bi-gear"></i> API Dokumentation
                            </a>
                        </div>
                    </div>
                </div>
            `}
        </div>

        <!-- Statistics -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-graph-up"></i> Regions-Statistiken</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-primary">${regions.length}</h4>
                                    <small class="text-muted">Gesamt Regionen</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-success">${regions.filter(r => r.enabled).length}</h4>
                                    <small class="text-muted">Aktive Regionen</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-warning">${regions.reduce((sum, r) => sum + (r.rankingCount || 0), 0)}</h4>
                                    <small class="text-muted">Gesamt Rankings</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-info">${regions.length > 0 ? Math.round(regions.reduce((sum, r) => sum + (r.rankingCount || 0), 0) / regions.length) : 0}</h4>
                                    <small class="text-muted">Ø Rankings/Region</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Regions -->
        ${regions.length > 0 ? `
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-trophy"></i> Top Regionen (nach Rankings)</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Rang</th>
                                            <th>Region</th>
                                            <th>ID</th>
                                            <th>Rankings</th>
                                            <th>Status</th>
                                            <th>Aktionen</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${regions
                                            .sort((a, b) => (b.rankingCount || 0) - (a.rankingCount || 0))
                                            .slice(0, 10)
                                            .map((region, index) => `
                                                <tr>
                                                    <td>
                                                        <span class="badge ${index < 3 ? 'bg-warning' : 'bg-secondary'}">
                                                            ${index + 1}
                                                        </span>
                                                    </td>
                                                    <td><strong>${region.name}</strong></td>
                                                    <td><code>${region.id}</code></td>
                                                    <td>${region.rankingCount || 0}</td>
                                                    <td>
                                                        <span class="badge ${region.enabled ? 'bg-success' : 'bg-secondary'}">
                                                            ${region.enabled ? 'Aktiv' : 'Inaktiv'}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="/rankings?regionId=${region.id}" class="btn btn-outline-primary btn-sm">
                                                                Rankings
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        ` : ''}

        <!-- DSV Regions Info -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <h6><i class="bi bi-info-circle"></i> DSV Regionen</h6>
                    <p class="mb-2">
                        Der Deutsche Schwimm-Verband (DSV) ist in verschiedene Regionen unterteilt. 
                        Jede Region hat ihre eigenen Wettkämpfe und Rankings.
                    </p>
                    <div class="row">
                        <div class="col-md-6">
                            <small><strong>Bekannte Regionen:</strong></small>
                            <ul class="small mb-0">
                                <li>Region 1: Schleswig-Holstein</li>
                                <li>Region 2: Hamburg</li>
                                <li>Region 3: Niedersachsen</li>
                                <li>Region 4: Bremen</li>
                                <li>Region 5: Nordrhein-Westfalen</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <small><strong>Weitere Regionen:</strong></small>
                            <ul class="small mb-0">
                                <li>Region 6-18: Weitere Bundesländer</li>
                                <li>Automatische Erkennung beim Scraping</li>
                                <li>Regionale Wettkampfdaten</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `
}) %>

<%- include('layouts/main', { 
    title: title || 'Rankings',
    currentPage: 'rankings',
    body: `
        <!-- <PERSON>er -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="bi bi-list-ol"></i> ${title || 'Rankings'}</h2>
                <p class="text-muted">Aktuelle Schwimm-Rankings aus dem DSV System</p>
            </div>
        </div>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-funnel"></i> Filter</h6>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="/rankings">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="eventId" class="form-label">Event</label>
                                    <select class="form-select" id="eventId" name="eventId">
                                        <option value="">Alle Events</option>
                                        ${events.map(event => `
                                            <option value="${event.id}" ${filters.eventId === event.id ? 'selected' : ''}>
                                                ${event.name}
                                            </option>
                                        `).join('')}
                                    </select>
                                </div>
                                
                                <div class="col-md-3 mb-3">
                                    <label for="regionId" class="form-label">Region</label>
                                    <select class="form-select" id="regionId" name="regionId">
                                        <option value="">Alle Regionen</option>
                                        ${regions.map(region => `
                                            <option value="${region.id}" ${filters.regionId === region.id ? 'selected' : ''}>
                                                ${region.name}
                                            </option>
                                        `).join('')}
                                    </select>
                                </div>
                                
                                <div class="col-md-2 mb-3">
                                    <label for="ageGroup" class="form-label">Jahrgang</label>
                                    <input type="text" class="form-control" id="ageGroup" name="ageGroup" 
                                           value="${filters.ageGroup || ''}" placeholder="z.B. 2010">
                                </div>
                                
                                <div class="col-md-2 mb-3">
                                    <label for="gender" class="form-label">Geschlecht</label>
                                    <select class="form-select" id="gender" name="gender">
                                        <option value="">Alle</option>
                                        <option value="M" ${filters.gender === 'M' ? 'selected' : ''}>Männlich</option>
                                        <option value="W" ${filters.gender === 'W' ? 'selected' : ''}>Weiblich</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-2 mb-3">
                                    <label for="limit" class="form-label">Anzahl</label>
                                    <select class="form-select" id="limit" name="limit">
                                        <option value="25" ${pagination.limit === 25 ? 'selected' : ''}>25</option>
                                        <option value="50" ${pagination.limit === 50 ? 'selected' : ''}>50</option>
                                        <option value="100" ${pagination.limit === 100 ? 'selected' : ''}>100</option>
                                        <option value="200" ${pagination.limit === 200 ? 'selected' : ''}>200</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-search"></i> Filtern
                                    </button>
                                    <a href="/rankings" class="btn btn-outline-secondary">
                                        <i class="bi bi-x-circle"></i> Zurücksetzen
                                    </a>
                                    <button type="button" class="btn btn-outline-success" onclick="DSVRankings.exportCSV(getFormData())">
                                        <i class="bi bi-download"></i> CSV Export
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="bi bi-trophy"></i> 
                            Rankings (${pagination.totalCount} Ergebnisse)
                        </h6>
                        <small class="text-muted">
                            Seite ${pagination.currentPage} von ${pagination.totalPages}
                        </small>
                    </div>
                    <div class="card-body p-0">
                        ${rankings.length > 0 ? `
                            <div class="table-responsive">
                                <table class="table table-striped table-hover mb-0">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Rang</th>
                                            <th>Name</th>
                                            <th>Jahrgang</th>
                                            <th>Verein</th>
                                            <th>Zeit</th>
                                            <th>Event</th>
                                            <th>Region</th>
                                            <th>Wettkampf</th>
                                            <th>Datum</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${rankings.map(ranking => `
                                            <tr>
                                                <td>
                                                    <span class="badge ${ranking.rank <= 3 ? 'bg-warning' : 'bg-secondary'}">
                                                        ${ranking.rank}
                                                    </span>
                                                </td>
                                                <td><strong>${ranking.swimmerName}</strong></td>
                                                <td>${ranking.birthYear || '-'}</td>
                                                <td>${ranking.club}</td>
                                                <td><code>${ranking.time}</code></td>
                                                <td>${ranking.event ? ranking.event.name : 'Unbekannt'}</td>
                                                <td>${ranking.region ? ranking.region.name : 'Unbekannt'}</td>
                                                <td>${ranking.competition || '-'}</td>
                                                <td>${ranking.date ? new Date(ranking.date).toLocaleDateString('de-DE') : '-'}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        ` : `
                            <div class="text-center py-5">
                                <i class="bi bi-search fs-1 text-muted"></i>
                                <h5 class="mt-3 text-muted">Keine Rankings gefunden</h5>
                                <p class="text-muted">
                                    Versuchen Sie andere Filterkriterien oder 
                                    <a href="/rankings">setzen Sie die Filter zurück</a>.
                                </p>
                            </div>
                        `}
                    </div>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        ${pagination.totalPages > 1 ? `
            <div class="row mt-4">
                <div class="col-12">
                    <nav aria-label="Rankings Pagination">
                        <ul class="pagination justify-content-center">
                            <li class="page-item ${!pagination.hasPrev ? 'disabled' : ''}">
                                <a class="page-link" href="?${new URLSearchParams({...filters, page: pagination.currentPage - 1}).toString()}">
                                    <i class="bi bi-chevron-left"></i> Zurück
                                </a>
                            </li>
                            
                            ${Array.from({length: Math.min(5, pagination.totalPages)}, (_, i) => {
                                const pageNum = Math.max(1, pagination.currentPage - 2) + i;
                                if (pageNum <= pagination.totalPages) {
                                    return `
                                        <li class="page-item ${pageNum === pagination.currentPage ? 'active' : ''}">
                                            <a class="page-link" href="?${new URLSearchParams({...filters, page: pageNum}).toString()}">
                                                ${pageNum}
                                            </a>
                                        </li>
                                    `;
                                }
                                return '';
                            }).join('')}
                            
                            <li class="page-item ${!pagination.hasNext ? 'disabled' : ''}">
                                <a class="page-link" href="?${new URLSearchParams({...filters, page: pagination.currentPage + 1}).toString()}">
                                    Weiter <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        ` : ''}

        <script>
            function getFormData() {
                const form = document.querySelector('form');
                const formData = new FormData(form);
                const data = {};
                for (let [key, value] of formData.entries()) {
                    if (value) data[key] = value;
                }
                return data;
            }
        </script>
    `
}) %>

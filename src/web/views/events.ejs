<%- include('layouts/main', { 
    title: title || 'Events',
    currentPage: 'events',
    body: `
        <!-- <PERSON> Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="bi bi-calendar-event"></i> ${title || 'Events'}</h2>
                <p class="text-muted">Verfügbare Schwimm-Events im DSV System</p>
            </div>
        </div>

        <!-- Events Grid -->
        <div class="row">
            ${events.length > 0 ? events.map(event => `
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 ${event.enabled ? '' : 'border-secondary'}">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">${event.name}</h6>
                            <span class="badge ${event.enabled ? 'bg-success' : 'bg-secondary'}">
                                ${event.enabled ? 'Aktiv' : 'Inaktiv'}
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">Code:</small>
                                    <div><code>${event.code}</code></div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">Distanz:</small>
                                    <div>${event.distance}m</div>
                                </div>
                            </div>
                            
                            <div class="row mt-2">
                                <div class="col-6">
                                    <small class="text-muted">Schwimmart:</small>
                                    <div>${event.stroke}</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">Bahn:</small>
                                    <div>${event.course === 'L' ? 'Langbahn' : 'Kurzbahn'}</div>
                                </div>
                            </div>
                            
                            <div class="row mt-2">
                                <div class="col-12">
                                    <small class="text-muted">Priorität:</small>
                                    <div>
                                        <span class="badge ${event.priority <= 3 ? 'bg-danger' : event.priority <= 6 ? 'bg-warning' : 'bg-info'}">
                                            ${event.priority}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="btn-group w-100" role="group">
                                <a href="/rankings?eventId=${event.id}" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-list-ol"></i> Rankings
                                </a>
                                <a href="/api/export/csv?eventId=${event.id}" class="btn btn-outline-success btn-sm">
                                    <i class="bi bi-download"></i> CSV
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('') : `
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="bi bi-calendar-x fs-1 text-muted"></i>
                            <h5 class="mt-3 text-muted">Keine Events verfügbar</h5>
                            <p class="text-muted">
                                Es sind noch keine Events konfiguriert. 
                                Bearbeiten Sie die Konfigurationsdatei oder verwenden Sie die API.
                            </p>
                            <a href="/api/events" class="btn btn-outline-primary">
                                <i class="bi bi-gear"></i> API Dokumentation
                            </a>
                        </div>
                    </div>
                </div>
            `}
        </div>

        <!-- Statistics -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-graph-up"></i> Event-Statistiken</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-primary">${events.length}</h4>
                                    <small class="text-muted">Gesamt Events</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-success">${events.filter(e => e.enabled).length}</h4>
                                    <small class="text-muted">Aktive Events</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-warning">${events.filter(e => e.course === 'L').length}</h4>
                                    <small class="text-muted">Langbahn</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-info">${events.filter(e => e.course === 'K').length}</h4>
                                    <small class="text-muted">Kurzbahn</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Event Types -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-pie-chart"></i> Schwimmarten</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            ${[...new Set(events.map(e => e.stroke))].map(stroke => `
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="text-center">
                                        <div class="badge bg-secondary fs-6 mb-2">${events.filter(e => e.stroke === stroke).length}</div>
                                        <div><small>${stroke}</small></div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Configuration Info -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <h6><i class="bi bi-info-circle"></i> Event-Konfiguration</h6>
                    <p class="mb-2">
                        Events werden in der Datei <code>src/scrapers/config/events.json</code> konfiguriert.
                        Neue Events können über die API hinzugefügt werden.
                    </p>
                    <div class="btn-group">
                        <a href="/api/events" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-code"></i> API Endpoints
                        </a>
                        <a href="/statistics" class="btn btn-outline-info btn-sm">
                            <i class="bi bi-graph-up"></i> Detaillierte Statistiken
                        </a>
                    </div>
                </div>
            </div>
        </div>
    `
}) %>

<%- include('layouts/main', { 
    title: title || 'Statistiken',
    currentPage: 'statistics',
    body: `
        <!-- <PERSON>er -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="bi bi-graph-up"></i> ${title || 'Statistiken'}</h2>
                <p class="text-muted">Detaillierte Statistiken und Analysen des DSV Scraper Systems</p>
            </div>
        </div>

        <!-- Overview Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h3>${statistics.totalRankings || 0}</h3>
                                <p class="mb-0">Gesamt Rankings</p>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-list-ol fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h3>${statistics.totalEvents || 0}</h3>
                                <p class="mb-0">Aktive Events</p>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-calendar-event fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h3>${statistics.totalRegions || 0}</h3>
                                <p class="mb-0">Regionen</p>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-geo fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h3>${statistics.recentScrapeLogs || 0}</h3>
                                <p class="mb-0">Scrape Logs</p>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-file-text fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Performers -->
        ${statistics.topPerformers && statistics.topPerformers.length > 0 ? `
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-trophy"></i> Top Performer</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Rang</th>
                                            <th>Name</th>
                                            <th>Jahrgang</th>
                                            <th>Verein</th>
                                            <th>Zeit</th>
                                            <th>Event</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${statistics.topPerformers.slice(0, 10).map((performer, index) => `
                                            <tr>
                                                <td>
                                                    <span class="badge ${index < 3 ? 'bg-warning' : 'bg-secondary'}">
                                                        ${index + 1}
                                                    </span>
                                                </td>
                                                <td><strong>${performer.swimmerName}</strong></td>
                                                <td>${performer.birthYear || '-'}</td>
                                                <td>${performer.club}</td>
                                                <td><code>${performer.time}</code></td>
                                                <td>${performer.event ? performer.event.name : 'Unbekannt'}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        ` : ''}

        <!-- System Information -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-gear"></i> System-Information</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Version:</strong></td>
                                <td>Node.js DSV Scraper v1.0.0</td>
                            </tr>
                            <tr>
                                <td><strong>Framework:</strong></td>
                                <td>Express.js + Prisma ORM</td>
                            </tr>
                            <tr>
                                <td><strong>Datenbank:</strong></td>
                                <td>SQLite (Development)</td>
                            </tr>
                            <tr>
                                <td><strong>Node.js Version:</strong></td>
                                <td>${process.version}</td>
                            </tr>
                            <tr>
                                <td><strong>Letztes Update:</strong></td>
                                <td>${statistics.lastUpdated ? new Date(statistics.lastUpdated).toLocaleString('de-DE') : 'Unbekannt'}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-activity"></i> Performance-Metriken</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Uptime:</strong></td>
                                <td>${Math.floor(process.uptime() / 3600)}h ${Math.floor((process.uptime() % 3600) / 60)}m</td>
                            </tr>
                            <tr>
                                <td><strong>Memory Usage:</strong></td>
                                <td>${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)} MB</td>
                            </tr>
                            <tr>
                                <td><strong>Platform:</strong></td>
                                <td>${process.platform} ${process.arch}</td>
                            </tr>
                            <tr>
                                <td><strong>Environment:</strong></td>
                                <td>${process.env.NODE_ENV || 'development'}</td>
                            </tr>
                            <tr>
                                <td><strong>Port:</strong></td>
                                <td>${process.env.PORT || 3000}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Distribution -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-pie-chart"></i> Event-Verteilung</h6>
                    </div>
                    <div class="card-body">
                        ${events && events.length > 0 ? `
                            <div class="row">
                                ${events.slice(0, 6).map(event => `
                                    <div class="col-6 mb-3">
                                        <div class="d-flex justify-content-between">
                                            <small>${event.name}</small>
                                            <span class="badge bg-primary">${event.rankingCount || 0}</span>
                                        </div>
                                        <div class="progress" style="height: 4px;">
                                            <div class="progress-bar" style="width: ${Math.min(100, (event.rankingCount || 0) / Math.max(1, statistics.totalRankings) * 100)}%"></div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        ` : `
                            <p class="text-muted text-center">Keine Event-Daten verfügbar</p>
                        `}
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-geo-alt"></i> Regions-Verteilung</h6>
                    </div>
                    <div class="card-body">
                        ${regions && regions.length > 0 ? `
                            <div class="row">
                                ${regions.slice(0, 6).map(region => `
                                    <div class="col-6 mb-3">
                                        <div class="d-flex justify-content-between">
                                            <small>${region.name}</small>
                                            <span class="badge bg-success">${region.rankingCount || 0}</span>
                                        </div>
                                        <div class="progress" style="height: 4px;">
                                            <div class="progress-bar bg-success" style="width: ${Math.min(100, (region.rankingCount || 0) / Math.max(1, statistics.totalRankings) * 100)}%"></div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        ` : `
                            <p class="text-muted text-center">Keine Regions-Daten verfügbar</p>
                        `}
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-lightning"></i> Schnellzugriff</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a href="/rankings" class="btn btn-outline-primary w-100">
                                    <i class="bi bi-list-ol"></i> Alle Rankings
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="/api/export/csv" class="btn btn-outline-success w-100">
                                    <i class="bi bi-download"></i> Vollständiger Export
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="/health" class="btn btn-outline-info w-100" target="_blank">
                                    <i class="bi bi-heart-pulse"></i> System Health
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="/logs" class="btn btn-outline-warning w-100">
                                    <i class="bi bi-file-text"></i> System Logs
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `
}) %>

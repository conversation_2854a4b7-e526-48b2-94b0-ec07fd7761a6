/**
 * API Routes für DSV Scraper - Node.js Version
 */
import express from 'express';
import { body, query, validationResult } from 'express-validator';
import { webLogger } from '../../utils/logger.js';

const router = express.Router();

// Validation Error Handler
const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            error: 'Validierungsfehler',
            details: errors.array()
        });
    }
    next();
};

// ==================== RANKINGS API ====================

/**
 * GET /api/rankings - Rankings abrufen
 */
router.get('/rankings', [
    query('eventId').optional().isString(),
    query('regionId').optional().isInt({ min: 1, max: 18 }),
    query('ageGroup').optional().isString(),
    query('gender').optional().isIn(['M', 'W']),
    query('season').optional().isString(),
    query('limit').optional().isInt({ min: 1, max: 1000 }).toInt(),
    query('page').optional().isInt({ min: 1 }).toInt()
], handleValidationErrors, async (req, res) => {
    try {
        const {
            eventId,
            regionId,
            ageGroup,
            gender,
            season,
            limit = 100,
            page = 1
        } = req.query;
        
        const filters = {
            ...(eventId && { eventId }),
            ...(regionId && { regionId: parseInt(regionId) }),
            ...(ageGroup && { ageGroup }),
            ...(gender && { gender }),
            ...(season && { season }),
            take: limit,
            skip: (page - 1) * limit
        };
        
        const rankings = await req.db.getRankings(filters);
        
        // Zusätzliche Metadaten
        const totalCount = await req.db.prisma.ranking.count({
            where: {
                ...(eventId && { eventId }),
                ...(regionId && { regionId: parseInt(regionId) }),
                ...(ageGroup && { ageGroup }),
                ...(gender && { gender }),
                ...(season && { season })
            }
        });
        
        res.json({
            data: rankings,
            pagination: {
                page,
                limit,
                total: totalCount,
                pages: Math.ceil(totalCount / limit)
            },
            filters: {
                eventId,
                regionId,
                ageGroup,
                gender,
                season
            }
        });
        
    } catch (error) {
        webLogger.error('❌ Fehler beim Abrufen der Rankings', {
            error: error.message,
            query: req.query
        });
        res.status(500).json({
            error: 'Fehler beim Abrufen der Rankings',
            message: error.message
        });
    }
});

/**
 * GET /api/rankings/top - Top Rankings abrufen
 */
router.get('/rankings/top', [
    query('eventId').optional().isString(),
    query('regionId').optional().isInt({ min: 1, max: 18 }),
    query('ageGroup').optional().isString(),
    query('gender').optional().isIn(['M', 'W']),
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt()
], handleValidationErrors, async (req, res) => {
    try {
        const {
            eventId,
            regionId,
            ageGroup,
            gender,
            limit = 10
        } = req.query;
        
        const filters = {
            ...(eventId && { eventId }),
            ...(regionId && { regionId: parseInt(regionId) }),
            ...(ageGroup && { ageGroup }),
            ...(gender && { gender }),
            take: limit
        };
        
        const topRankings = await req.db.getRankings(filters);
        
        res.json({
            data: topRankings,
            count: topRankings.length
        });
        
    } catch (error) {
        webLogger.error('❌ Fehler beim Abrufen der Top Rankings', {
            error: error.message,
            query: req.query
        });
        res.status(500).json({
            error: 'Fehler beim Abrufen der Top Rankings',
            message: error.message
        });
    }
});

// ==================== EVENTS API ====================

/**
 * GET /api/events - Events abrufen
 */
router.get('/events', async (req, res) => {
    try {
        const events = await req.db.getEvents();
        res.json({
            data: events,
            count: events.length
        });
    } catch (error) {
        webLogger.error('❌ Fehler beim Abrufen der Events', {
            error: error.message
        });
        res.status(500).json({
            error: 'Fehler beim Abrufen der Events',
            message: error.message
        });
    }
});

// ==================== REGIONS API ====================

/**
 * GET /api/regions - Regionen abrufen
 */
router.get('/regions', async (req, res) => {
    try {
        const regions = await req.db.getRegions();
        res.json({
            data: regions,
            count: regions.length
        });
    } catch (error) {
        webLogger.error('❌ Fehler beim Abrufen der Regionen', {
            error: error.message
        });
        res.status(500).json({
            error: 'Fehler beim Abrufen der Regionen',
            message: error.message
        });
    }
});

// ==================== STATISTICS API ====================

/**
 * GET /api/statistics - Statistiken abrufen
 */
router.get('/statistics', async (req, res) => {
    try {
        const statistics = await req.db.getStatistics();
        res.json(statistics);
    } catch (error) {
        webLogger.error('❌ Fehler beim Abrufen der Statistiken', {
            error: error.message
        });
        res.status(500).json({
            error: 'Fehler beim Abrufen der Statistiken',
            message: error.message
        });
    }
});

// ==================== SCRAPE LOGS API ====================

/**
 * GET /api/scrape-logs - Scrape Logs abrufen
 */
router.get('/scrape-logs', [
    query('limit').optional().isInt({ min: 1, max: 200 }).toInt()
], handleValidationErrors, async (req, res) => {
    try {
        const { limit = 50 } = req.query;
        const scrapeLogs = await req.db.getRecentScrapeLogs(limit);
        
        res.json({
            data: scrapeLogs,
            count: scrapeLogs.length
        });
    } catch (error) {
        webLogger.error('❌ Fehler beim Abrufen der Scrape Logs', {
            error: error.message
        });
        res.status(500).json({
            error: 'Fehler beim Abrufen der Scrape Logs',
            message: error.message
        });
    }
});

// ==================== EXPORT API ====================

/**
 * GET /api/export/csv - CSV Export
 */
router.get('/export/csv', [
    query('eventId').optional().isString(),
    query('regionId').optional().isInt({ min: 1, max: 18 }),
    query('ageGroup').optional().isString(),
    query('gender').optional().isIn(['M', 'W']),
    query('season').optional().isString()
], handleValidationErrors, async (req, res) => {
    try {
        const {
            eventId,
            regionId,
            ageGroup,
            gender,
            season
        } = req.query;
        
        const filters = {
            ...(eventId && { eventId }),
            ...(regionId && { regionId: parseInt(regionId) }),
            ...(ageGroup && { ageGroup }),
            ...(gender && { gender }),
            ...(season && { season }),
            take: 10000 // Maximale Anzahl für Export
        };
        
        const rankings = await req.db.getRankings(filters);
        
        // CSV Header
        const csvHeader = [
            'Rang',
            'Name',
            'Geburtsjahr',
            'Verein',
            'Zeit',
            'Event',
            'Region',
            'Altersgruppe',
            'Geschlecht',
            'Wettkampf',
            'Datum',
            'Ort'
        ].join(',');
        
        // CSV Daten
        const csvData = rankings.map(ranking => [
            ranking.rank,
            `"${ranking.swimmerName}"`,
            ranking.birthYear || '',
            `"${ranking.club}"`,
            ranking.time,
            `"${ranking.event.name}"`,
            `"${ranking.region.name}"`,
            ranking.ageGroup,
            ranking.gender,
            `"${ranking.competition || ''}"`,
            ranking.date ? ranking.date.toISOString().split('T')[0] : '',
            `"${ranking.location || ''}"`
        ].join(',')).join('\n');
        
        const csv = `${csvHeader}\n${csvData}`;
        
        // Dateiname generieren
        const timestamp = new Date().toISOString().split('T')[0];
        const filename = `dsv-rankings-${timestamp}.csv`;
        
        res.setHeader('Content-Type', 'text/csv; charset=utf-8');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        res.send('\ufeff' + csv); // BOM für korrekte UTF-8 Darstellung in Excel
        
    } catch (error) {
        webLogger.error('❌ Fehler beim CSV Export', {
            error: error.message,
            query: req.query
        });
        res.status(500).json({
            error: 'Fehler beim CSV Export',
            message: error.message
        });
    }
});

// Manual Scraping Endpoints
router.post('/scrape/manual', async (req, res) => {
    try {
        const { DSVScraper } = await import('../../scrapers/DSVScraper.js');
        const { DataProcessor } = await import('../../utils/DataProcessor.js');

        const scraper = new DSVScraper('src/scrapers/config/events.json');
        const processor = new DataProcessor(req.db);

        // Start scraping in background
        const scrapingPromise = (async () => {
            await scraper.loadConfig();
            const results = await scraper.scrapeAllEvents();
            return await processor.processScrapingResults(results);
        })();

        // Return immediately with job ID
        const jobId = `manual-${Date.now()}`;

        res.json({
            success: true,
            message: 'Manual scraping started',
            jobId: jobId,
            timestamp: new Date().toISOString()
        });

        // Handle scraping completion in background
        scrapingPromise.then(results => {
            console.log(`Manual scraping ${jobId} completed:`, Object.keys(results).length, 'events processed');
        }).catch(error => {
            console.error(`Manual scraping ${jobId} failed:`, error);
        });

    } catch (error) {
        webLogger.error('❌ Manual scraping error', { error: error.message });
        res.status(500).json({
            error: 'Fehler beim manuellen Scraping',
            details: error.message
        });
    }
});

router.post('/scrape/event', async (req, res) => {
    try {
        const { eventName, ageGroup, gender, regionId } = req.body;

        if (!eventName || !ageGroup || !gender) {
            return res.status(400).json({
                error: 'Validierungsfehler',
                details: 'eventName, ageGroup und gender sind erforderlich'
            });
        }

        const { DSVScraper } = await import('../../scrapers/DSVScraper.js');
        const { DataProcessor } = await import('../../utils/DataProcessor.js');

        const scraper = new DSVScraper();
        const processor = new DataProcessor(req.db);

        // Create scraping configuration
        const config = {
            eventName,
            ageGroup,
            gender,
            regionId: regionId || 1,
            season: '2025',
            timeRange: '01.06.2024|31.05.2025'
        };

        // Scrape single event
        const rankings = await scraper.scrapeEvent(config);

        // Process results if any
        let saved = 0;
        if (rankings.length > 0) {
            const results = { [`${eventName}_${ageGroup}_${gender}_${config.regionId}`]: rankings };
            const processed = await processor.processScrapingResults(results);
            saved = Object.values(processed)[0]?.saved || 0;
        }

        res.json({
            success: true,
            message: 'Event scraping completed',
            event: eventName,
            ageGroup,
            gender,
            regionId: config.regionId,
            rankingsFound: rankings.length,
            rankingsSaved: saved,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        webLogger.error('❌ Event scraping error', { error: error.message });
        res.status(500).json({
            error: 'Fehler beim Event-Scraping',
            details: error.message
        });
    }
});

router.get('/scrape/status', async (req, res) => {
    try {
        // Get recent scrape logs
        const recentLogs = await req.db.getRecentScrapeLogs(10);

        // Get system status
        const health = await req.db.healthCheck();

        res.json({
            success: true,
            status: {
                database: health.status,
                lastScrape: recentLogs[0] || null,
                recentActivity: recentLogs.length,
                scheduler: process.env.ENABLE_SCHEDULER === 'true' ? 'enabled' : 'disabled'
            },
            recentLogs: recentLogs.slice(0, 5)
        });

    } catch (error) {
        webLogger.error('❌ Scrape status error', { error: error.message });
        res.status(500).json({
            error: 'Fehler beim Abrufen des Scraping-Status',
            details: error.message
        });
    }
});

export default router;

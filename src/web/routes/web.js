/**
 * Web Routes für DSV Scraper Dashboard - Node.js Version
 */
import express from 'express';
import { webLogger } from '../../utils/logger.js';

const router = express.Router();

/**
 * Homepage - Dashboard
 */
router.get('/', async (req, res) => {
    try {
        // Aktuelle Statistiken abrufen
        const statistics = await req.db.getStatistics();
        
        // Aktuelle Events und Regionen
        const [events, regions] = await Promise.all([
            req.db.getEvents(),
            req.db.getRegions()
        ]);
        
        res.render('dashboard', {
            title: 'DSV Rankings Dashboard',
            statistics,
            events,
            regions,
            currentPage: 'dashboard'
        });
        
    } catch (error) {
        webLogger.error('❌ Fehler beim Laden des Dashboards', {
            error: error.message
        });
        res.status(500).render('error', {
            title: 'Fehler',
            error: {
                status: 500,
                message: '<PERSON><PERSON> beim <PERSON>den des Dashboards'
            }
        });
    }
});

/**
 * Rankings Seite
 */
router.get('/rankings', async (req, res) => {
    try {
        const {
            eventId,
            regionId,
            ageGroup,
            gender,
            season,
            page = 1,
            limit = 50
        } = req.query;
        
        // Filter für Datenbankabfrage
        const filters = {
            ...(eventId && { eventId }),
            ...(regionId && { regionId: parseInt(regionId) }),
            ...(ageGroup && { ageGroup }),
            ...(gender && { gender }),
            ...(season && { season }),
            take: parseInt(limit),
            skip: (parseInt(page) - 1) * parseInt(limit)
        };
        
        // Daten abrufen
        const [rankings, events, regions] = await Promise.all([
            req.db.getRankings(filters),
            req.db.getEvents(),
            req.db.getRegions()
        ]);
        
        // Gesamtanzahl für Pagination
        const totalCount = await req.db.prisma.ranking.count({
            where: {
                ...(eventId && { eventId }),
                ...(regionId && { regionId: parseInt(regionId) }),
                ...(ageGroup && { ageGroup }),
                ...(gender && { gender }),
                ...(season && { season })
            }
        });
        
        const totalPages = Math.ceil(totalCount / parseInt(limit));
        
        res.render('rankings', {
            title: 'Rankings',
            rankings,
            events,
            regions,
            filters: {
                eventId,
                regionId: regionId ? parseInt(regionId) : null,
                ageGroup,
                gender,
                season
            },
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalCount,
                limit: parseInt(limit),
                hasNext: parseInt(page) < totalPages,
                hasPrev: parseInt(page) > 1
            },
            currentPage: 'rankings'
        });
        
    } catch (error) {
        webLogger.error('❌ Fehler beim Laden der Rankings', {
            error: error.message,
            query: req.query
        });
        res.status(500).render('error', {
            title: 'Fehler',
            error: {
                status: 500,
                message: 'Fehler beim Laden der Rankings'
            }
        });
    }
});

/**
 * Events Seite
 */
router.get('/events', async (req, res) => {
    try {
        const events = await req.db.getEvents(false); // Alle Events, auch deaktivierte
        
        res.render('events', {
            title: 'Events',
            events,
            currentPage: 'events'
        });
        
    } catch (error) {
        webLogger.error('❌ Fehler beim Laden der Events', {
            error: error.message
        });
        res.status(500).render('error', {
            title: 'Fehler',
            error: {
                status: 500,
                message: 'Fehler beim Laden der Events'
            }
        });
    }
});

/**
 * Regionen Seite
 */
router.get('/regions', async (req, res) => {
    try {
        const regions = await req.db.getRegions(false); // Alle Regionen, auch deaktivierte
        
        // Statistiken pro Region
        const regionStats = await Promise.all(
            regions.map(async (region) => {
                const rankingCount = await req.db.prisma.ranking.count({
                    where: { regionId: region.id }
                });
                return {
                    ...region,
                    rankingCount
                };
            })
        );
        
        res.render('regions', {
            title: 'Regionen',
            regions: regionStats,
            currentPage: 'regions'
        });
        
    } catch (error) {
        webLogger.error('❌ Fehler beim Laden der Regionen', {
            error: error.message
        });
        res.status(500).render('error', {
            title: 'Fehler',
            error: {
                status: 500,
                message: 'Fehler beim Laden der Regionen'
            }
        });
    }
});

/**
 * Logs Seite
 */
router.get('/logs', async (req, res) => {
    try {
        const { limit = 100 } = req.query;
        
        const scrapeLogs = await req.db.getRecentScrapeLogs(parseInt(limit));
        
        res.render('logs', {
            title: 'Scraping Logs',
            scrapeLogs,
            currentPage: 'logs'
        });
        
    } catch (error) {
        webLogger.error('❌ Fehler beim Laden der Logs', {
            error: error.message
        });
        res.status(500).render('error', {
            title: 'Fehler',
            error: {
                status: 500,
                message: 'Fehler beim Laden der Logs'
            }
        });
    }
});

/**
 * Statistiken Seite
 */
router.get('/statistics', async (req, res) => {
    try {
        const statistics = await req.db.getStatistics();
        
        // Zusätzliche Statistiken
        const additionalStats = await Promise.all([
            // Rankings pro Event
            req.db.prisma.ranking.groupBy({
                by: ['eventId'],
                _count: { _all: true },
                include: {
                    event: true
                }
            }),
            // Rankings pro Region
            req.db.prisma.ranking.groupBy({
                by: ['regionId'],
                _count: { _all: true }
            }),
            // Rankings pro Geschlecht
            req.db.prisma.ranking.groupBy({
                by: ['gender'],
                _count: { _all: true }
            })
        ]);
        
        res.render('statistics', {
            title: 'Statistiken',
            statistics,
            additionalStats: {
                byEvent: additionalStats[0],
                byRegion: additionalStats[1],
                byGender: additionalStats[2]
            },
            currentPage: 'statistics'
        });
        
    } catch (error) {
        webLogger.error('❌ Fehler beim Laden der Statistiken', {
            error: error.message
        });
        res.status(500).render('error', {
            title: 'Fehler',
            error: {
                status: 500,
                message: 'Fehler beim Laden der Statistiken'
            }
        });
    }
});

/**
 * About Seite
 */
router.get('/about', (req, res) => {
    res.render('about', {
        title: 'Über DSV Scraper',
        currentPage: 'about'
    });
});

export default router;

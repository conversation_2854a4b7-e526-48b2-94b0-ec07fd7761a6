/**
 * Express Web Application für DSV Rankings Dashboard - Node.js Version
 */
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import path from 'path';
import { fileURLToPath } from 'url';

import config from '../config.js';
import { webLogger, logWebRequest } from '../utils/logger.js';
import DatabaseManager from '../utils/DatabaseManager.js';

// Routes importieren
import apiRoutes from './routes/api.js';
import webRoutes from './routes/web.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Express App erstellen und konfigurieren
 */
export function createApp() {
    const app = express();
    
    // Trust proxy für korrekte IP-Adressen
    app.set('trust proxy', 1);
    
    // View Engine Setup
    app.set('view engine', 'ejs');
    app.set('views', path.join(__dirname, 'views'));
    
    // Security Middleware
    app.use(helmet({
        contentSecurityPolicy: {
            directives: {
                defaultSrc: ["'self'"],
                styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
                scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
                imgSrc: ["'self'", "data:", "https:"],
                fontSrc: ["'self'", "https://cdn.jsdelivr.net"]
            }
        }
    }));
    
    // CORS Configuration
    app.use(cors({
        origin: config.CORS_ORIGINS,
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    }));
    
    // Rate Limiting
    const limiter = rateLimit({
        windowMs: config.RATE_LIMIT_WINDOW,
        max: config.RATE_LIMIT_MAX,
        message: {
            error: 'Zu viele Anfragen. Bitte versuchen Sie es später erneut.',
            retryAfter: Math.ceil(config.RATE_LIMIT_WINDOW / 1000)
        },
        standardHeaders: true,
        legacyHeaders: false
    });
    app.use('/api/', limiter);
    
    // Body Parser Middleware
    app.use(express.json({ limit: '10mb' }));
    app.use(express.urlencoded({ extended: true, limit: '10mb' }));
    
    // Compression
    app.use(compression());
    
    // Static Files
    app.use('/static', express.static(path.join(__dirname, 'public'), {
        maxAge: config.NODE_ENV === 'production' ? '1d' : '0',
        etag: true
    }));
    
    // Request Logging Middleware
    app.use((req, res, next) => {
        const startTime = Date.now();
        
        res.on('finish', () => {
            const duration = Date.now() - startTime;
            logWebRequest(req, res, duration);
        });
        
        next();
    });
    
    // Database Middleware
    app.use(async (req, res, next) => {
        try {
            if (!req.app.locals.db) {
                const db = new DatabaseManager();
                await db.connect();
                req.app.locals.db = db;
            }
            req.db = req.app.locals.db;
            next();
        } catch (error) {
            webLogger.error('❌ Datenbankverbindung fehlgeschlagen', {
                error: error.message,
                url: req.url
            });
            res.status(500).json({
                error: 'Datenbankverbindung fehlgeschlagen',
                message: config.NODE_ENV === 'development' ? error.message : 'Interner Serverfehler'
            });
        }
    });
    
    // Routes
    app.use('/api', apiRoutes);
    app.use('/', webRoutes);
    
    // Health Check Endpoint
    app.get('/health', async (req, res) => {
        try {
            const dbHealth = await req.db.healthCheck();
            
            res.json({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                version: process.env.npm_package_version || '1.0.0',
                environment: config.NODE_ENV,
                database: dbHealth,
                uptime: process.uptime(),
                memory: process.memoryUsage()
            });
        } catch (error) {
            res.status(503).json({
                status: 'unhealthy',
                timestamp: new Date().toISOString(),
                error: error.message
            });
        }
    });
    
    // 404 Handler
    app.use((req, res) => {
        if (req.path.startsWith('/api/')) {
            res.status(404).json({
                error: 'API Endpoint nicht gefunden',
                path: req.path,
                method: req.method
            });
        } else {
            res.status(404).render('error', {
                title: 'Seite nicht gefunden',
                error: {
                    status: 404,
                    message: 'Die angeforderte Seite wurde nicht gefunden.'
                }
            });
        }
    });
    
    // Error Handler
    app.use((error, req, res, next) => {
        webLogger.error('❌ Unbehandelter Fehler', {
            error: error.message,
            stack: error.stack,
            url: req.url,
            method: req.method,
            userAgent: req.get('User-Agent'),
            ip: req.ip
        });
        
        const status = error.status || 500;
        const message = config.NODE_ENV === 'development' ? error.message : 'Interner Serverfehler';
        
        if (req.path.startsWith('/api/')) {
            res.status(status).json({
                error: message,
                ...(config.NODE_ENV === 'development' && { stack: error.stack })
            });
        } else {
            res.status(status).render('error', {
                title: 'Fehler',
                error: {
                    status,
                    message
                }
            });
        }
    });
    
    return app;
}

/**
 * Server starten
 */
export function startServer(app) {
    const port = config.PORT;
    
    const server = app.listen(port, () => {
        webLogger.info('🚀 DSV Scraper Server gestartet', {
            port,
            environment: config.NODE_ENV,
            pid: process.pid
        });
    });
    
    // Graceful Shutdown
    const gracefulShutdown = async (signal) => {
        webLogger.info(`📴 ${signal} empfangen. Starte Graceful Shutdown...`);
        
        server.close(async () => {
            webLogger.info('🔒 HTTP Server geschlossen');
            
            // Datenbankverbindung schließen
            if (app.locals.db) {
                await app.locals.db.disconnect();
            }
            
            webLogger.info('✅ Graceful Shutdown abgeschlossen');
            process.exit(0);
        });
        
        // Force shutdown nach 30 Sekunden
        setTimeout(() => {
            webLogger.error('⚠️ Force Shutdown nach Timeout');
            process.exit(1);
        }, 30000);
    };
    
    // Signal Handler
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    
    // Unhandled Promise Rejections
    process.on('unhandledRejection', (reason, promise) => {
        webLogger.error('❌ Unhandled Promise Rejection', {
            reason: reason?.message || reason,
            stack: reason?.stack,
            promise
        });
    });
    
    // Uncaught Exceptions
    process.on('uncaughtException', (error) => {
        webLogger.error('❌ Uncaught Exception', {
            error: error.message,
            stack: error.stack
        });
        process.exit(1);
    });
    
    return server;
}

export default { createApp, startServer };

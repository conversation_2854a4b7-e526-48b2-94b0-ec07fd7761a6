/**
 * DSV Rankings - Frontend JavaScript
 */

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DSV Rankings - Node.js Version loaded');
    
    // Add fade-in animation to main content
    const mainContent = document.querySelector('main');
    if (mainContent) {
        mainContent.classList.add('fade-in');
    }
    
    // Initialize tooltips if Bootstrap is available
    if (typeof bootstrap !== 'undefined') {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    // Auto-refresh health status if on health page
    if (window.location.pathname === '/health') {
        setInterval(refreshHealthStatus, 30000); // Refresh every 30 seconds
    }
});

// Health status refresh function
function refreshHealthStatus() {
    fetch('/health')
        .then(response => response.json())
        .then(data => {
            console.log('Health status updated:', data);
            // Update UI if needed
        })
        .catch(error => {
            console.error('Error refreshing health status:', error);
        });
}

// Utility function to format numbers
function formatNumber(num) {
    return new Intl.NumberFormat('de-DE').format(num);
}

// Utility function to format dates
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('de-DE', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Export CSV function
function exportCSV(filters = {}) {
    const params = new URLSearchParams(filters);
    const url = `/api/export/csv?${params.toString()}`;
    
    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = url;
    link.download = `dsv-rankings-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Show loading spinner
function showLoading(element) {
    if (element) {
        element.innerHTML = '<div class="text-center"><div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div></div>';
    }
}

// Hide loading spinner
function hideLoading(element, originalContent) {
    if (element) {
        element.innerHTML = originalContent;
    }
}

// API helper function
async function apiRequest(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('API request failed:', error);
        throw error;
    }
}

// Global error handler
window.addEventListener('error', function(event) {
    console.error('Global error:', event.error);
});

// Global unhandled promise rejection handler
window.addEventListener('unhandledrejection', function(event) {
    console.error('Unhandled promise rejection:', event.reason);
});

// Scraping functions
async function startScraping(type = 'manual', options = {}) {
    try {
        const endpoint = type === 'event' ? '/api/scrape/event' : '/api/scrape/manual';
        const method = 'POST';
        const body = type === 'event' ? JSON.stringify(options) : undefined;

        const response = await apiRequest(endpoint, {
            method,
            body
        });

        return response;
    } catch (error) {
        console.error('Scraping failed:', error);
        throw error;
    }
}

async function checkScrapingStatus() {
    try {
        return await apiRequest('/api/scrape/status');
    } catch (error) {
        console.error('Failed to check scraping status:', error);
        throw error;
    }
}

// Enhanced button loading state
function setButtonLoading(button, loading = true) {
    if (loading) {
        button.disabled = true;
        button.classList.add('loading');
        button.dataset.originalText = button.innerHTML;
    } else {
        button.disabled = false;
        button.classList.remove('loading');
        if (button.dataset.originalText) {
            button.innerHTML = button.dataset.originalText;
        }
    }
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Make functions available globally
window.DSVRankings = {
    formatNumber,
    formatDate,
    exportCSV,
    showLoading,
    hideLoading,
    apiRequest,
    startScraping,
    checkScrapingStatus,
    setButtonLoading,
    showNotification
};

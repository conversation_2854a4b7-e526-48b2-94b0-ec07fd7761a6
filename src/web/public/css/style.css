/* DSV Rankings - Custom Styles */

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.nav-link.active {
    font-weight: bold;
}

/* Cards */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Statistics Cards */
.card.bg-info,
.card.bg-success,
.card.bg-warning,
.card.bg-secondary {
    border: none;
}

.card.bg-info:hover,
.card.bg-success:hover,
.card.bg-warning:hover,
.card.bg-secondary:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease-in-out;
}

/* Jumbotron */
.jumbotron {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

/* Tables */
.table {
    background-color: white;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

/* Buttons */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

.btn-outline-primary:hover,
.btn-outline-success:hover,
.btn-outline-info:hover {
    transform: translateY(-1px);
    transition: transform 0.2s ease-in-out;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .display-4 {
        font-size: 2rem;
    }
    
    .card-body h4 {
        font-size: 1.5rem;
    }
}

/* Custom animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* Status indicators */
.status-healthy {
    color: #28a745;
}

.status-warning {
    color: #ffc107;
}

.status-error {
    color: #dc3545;
}

/* Code blocks */
pre {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 1rem;
    font-size: 0.875rem;
}

/* Utility classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Scraping UI */
.scraping-actions .card {
    border: 2px solid #007bff;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 123, 255, 0.15);
}

.scraping-actions .card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.scraping-actions .btn {
    transition: all 0.3s ease;
}

.scraping-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.scraping-status {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.scraping-results {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Modal enhancements */
.modal-content {
    border-radius: 0.5rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
    border-bottom: 2px solid #dee2e6;
}

.modal-footer {
    border-top: 2px solid #dee2e6;
}

/* Button loading state */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

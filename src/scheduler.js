/**
 * Scheduler Entry Point für DSV Scraper - Node.js Version
 */
import { Client } from 'appwrite';
import config from './config.js';
import { schedulerLogger } from './utils/logger.js';
import ScheduledScraper from './scheduler/CronJobs.js';
import AppwriteDatabaseManager from './utils/AppwriteDatabase.js';

/**
 * Appwrite Scheduler Function Entry Point
 */
export async function main(req, res) {
    try {
        schedulerLogger.info('🚀 Starte DSV Scheduler Function');
        
        // Bestimme Aktion aus Query Parameter
        const action = req.query?.action || 'weekly_scrape';
        
        let result = {};
        
        switch (action) {
            case 'weekly_scrape':
                result = await executeWeeklyScrape();
                break;
                
            case 'manual_scrape':
                result = await executeManualScrape(req.query);
                break;
                
            case 'health_check':
                result = await executeHealthCheck();
                break;
                
            case 'cleanup':
                result = await executeCleanup();
                break;
                
            default:
                throw new Error(`Unbekannte Aktion: ${action}`);
        }
        
        schedulerLogger.info('✅ Scheduler Function abgeschlossen', {
            action,
            result
        });
        
        if (res && res.send) {
            return res.send(JSON.stringify({
                success: true,
                action,
                result,
                timestamp: new Date().toISOString()
            }), 200);
        }
        
        return {
            success: true,
            action,
            result,
            timestamp: new Date().toISOString()
        };
        
    } catch (error) {
        schedulerLogger.error('❌ Fehler in Scheduler Function', {
            error: error.message,
            stack: error.stack,
            action: req.query?.action
        });
        
        if (res && res.send) {
            return res.send(JSON.stringify({
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            }), 500);
        }
        
        throw error;
    }
}

/**
 * Wöchentliches Scraping ausführen
 */
async function executeWeeklyScrape() {
    const startTime = Date.now();
    
    try {
        // Scheduler initialisieren
        const scheduler = new ScheduledScraper();
        
        // Für Appwrite: Database Manager ersetzen
        if (config.isAppwriteEnabled()) {
            const client = new Client();
            client.set_endpoint(config.APPWRITE_ENDPOINT);
            client.set_project(config.APPWRITE_PROJECT_ID);
            client.set_key(config.APPWRITE_API_KEY);
            
            scheduler.db = new AppwriteDatabaseManager(client);
        }
        
        await scheduler.initialize();
        
        // Wöchentliches Scraping durchführen
        await scheduler.weeklyScrapingJob();
        
        const duration = Date.now() - startTime;
        
        return {
            type: 'weekly_scrape',
            status: 'success',
            duration: `${duration}ms`,
            stats: scheduler.lastRunStats
        };
        
    } catch (error) {
        const duration = Date.now() - startTime;
        
        return {
            type: 'weekly_scrape',
            status: 'error',
            duration: `${duration}ms`,
            error: error.message
        };
    }
}

/**
 * Manuelles Scraping ausführen
 */
async function executeManualScrape(queryParams) {
    const startTime = Date.now();
    
    try {
        const {
            eventId,
            regionId,
            ageGroup,
            gender,
            season
        } = queryParams;
        
        // Scheduler initialisieren
        const scheduler = new ScheduledScraper();
        
        // Für Appwrite: Database Manager ersetzen
        if (config.isAppwriteEnabled()) {
            const client = new Client();
            client.set_endpoint(config.APPWRITE_ENDPOINT);
            client.set_project(config.APPWRITE_PROJECT_ID);
            client.set_key(config.APPWRITE_API_KEY);
            
            scheduler.db = new AppwriteDatabaseManager(client);
        }
        
        await scheduler.initialize();
        
        // Spezifisches Event scrapen (falls Parameter gegeben)
        let results;
        if (eventId || regionId || ageGroup || gender) {
            // Einzelnes Event scrapen
            const scrapingConfig = {
                eventName: eventId || 'Manual Scrape',
                eventCode: eventId || '',
                regionId: regionId ? parseInt(regionId) : 1,
                ageGroup: ageGroup || '2015',
                gender: gender || 'M',
                season: season || '2025'
            };
            
            results = { manual: await scheduler.scraper.scrapeEvent(scrapingConfig) };
        } else {
            // Alle Events scrapen
            results = await scheduler.scraper.scrapeAllEvents();
        }
        
        // Daten verarbeiten
        const processedResults = await scheduler.processor.processScrapingResults(results);
        const stats = scheduler.calculateStats(processedResults);
        
        const duration = Date.now() - startTime;
        
        return {
            type: 'manual_scrape',
            status: 'success',
            duration: `${duration}ms`,
            stats,
            parameters: queryParams
        };
        
    } catch (error) {
        const duration = Date.now() - startTime;
        
        return {
            type: 'manual_scrape',
            status: 'error',
            duration: `${duration}ms`,
            error: error.message,
            parameters: queryParams
        };
    }
}

/**
 * Gesundheitsprüfung ausführen
 */
async function executeHealthCheck() {
    try {
        const scheduler = new ScheduledScraper();
        
        // Für Appwrite: Database Manager ersetzen
        if (config.isAppwriteEnabled()) {
            const client = new Client();
            client.set_endpoint(config.APPWRITE_ENDPOINT);
            client.set_project(config.APPWRITE_PROJECT_ID);
            client.set_key(config.APPWRITE_API_KEY);
            
            scheduler.db = new AppwriteDatabaseManager(client);
        } else {
            await scheduler.initialize();
        }
        
        const health = await scheduler.db.healthCheck();
        
        return {
            type: 'health_check',
            status: health.status,
            details: health
        };
        
    } catch (error) {
        return {
            type: 'health_check',
            status: 'error',
            error: error.message
        };
    }
}

/**
 * Bereinigung ausführen
 */
async function executeCleanup() {
    const startTime = Date.now();
    
    try {
        const scheduler = new ScheduledScraper();
        
        // Für Appwrite: Database Manager ersetzen
        if (config.isAppwriteEnabled()) {
            const client = new Client();
            client.set_endpoint(config.APPWRITE_ENDPOINT);
            client.set_project(config.APPWRITE_PROJECT_ID);
            client.set_key(config.APPWRITE_API_KEY);
            
            scheduler.db = new AppwriteDatabaseManager(client);
        } else {
            await scheduler.initialize();
        }
        
        await scheduler.dailyCleanupJob();
        
        const duration = Date.now() - startTime;
        
        return {
            type: 'cleanup',
            status: 'success',
            duration: `${duration}ms`
        };
        
    } catch (error) {
        const duration = Date.now() - startTime;
        
        return {
            type: 'cleanup',
            status: 'error',
            duration: `${duration}ms`,
            error: error.message
        };
    }
}

/**
 * Standalone Scheduler (für lokale Entwicklung)
 */
async function startStandaloneScheduler() {
    try {
        schedulerLogger.info('🚀 Starte DSV Scheduler (Standalone)');
        
        const scheduler = new ScheduledScraper();
        await scheduler.startAllJobs();
        
        // Graceful Shutdown
        process.on('SIGTERM', () => {
            schedulerLogger.info('📴 SIGTERM empfangen. Stoppe Scheduler...');
            scheduler.stopAllJobs();
            process.exit(0);
        });
        
        process.on('SIGINT', () => {
            schedulerLogger.info('📴 SIGINT empfangen. Stoppe Scheduler...');
            scheduler.stopAllJobs();
            process.exit(0);
        });
        
        schedulerLogger.info('✅ Scheduler läuft. Drücke Ctrl+C zum Beenden.');
        
    } catch (error) {
        schedulerLogger.error('❌ Fehler beim Starten des Schedulers', {
            error: error.message,
            stack: error.stack
        });
        process.exit(1);
    }
}

// Prüfe ob das Skript direkt ausgeführt wird (nicht als Appwrite Function)
if (import.meta.url === `file://${process.argv[1]}`) {
    startStandaloneScheduler();
}

export default main;

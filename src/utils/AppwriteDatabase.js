/**
 * Appwrite Database Manager für DSV Scraper - Node.js Version
 */
import { Databases, Query } from 'appwrite';
import config from '../config.js';
import { databaseLogger } from './logger.js';

export class AppwriteDatabaseManager {
    constructor(client) {
        this.client = client;
        this.databases = new Databases(client);
        this.databaseId = config.APPWRITE_DATABASE_ID;
        
        // Collection IDs
        this.collections = {
            events: 'events',
            regions: 'regions',
            rankings: 'rankings',
            scrapeLogs: 'scrape_logs',
            schedulerJobs: 'scheduler_jobs',
            notifications: 'notifications',
            settings: 'settings'
        };
    }
    
    /**
     * Gesundheitsprüfung
     */
    async healthCheck() {
        try {
            await this.databases.listDocuments(this.databaseId, this.collections.events, [
                Query.limit(1)
            ]);
            
            return {
                status: 'healthy',
                timestamp: new Date(),
                database: 'appwrite'
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                error: error.message,
                timestamp: new Date(),
                database: 'appwrite'
            };
        }
    }
    
    // ==================== EVENTS ====================
    
    /**
     * Events abrufen
     */
    async getEvents(enabledOnly = true) {
        try {
            const queries = [Query.orderAsc('priority')];
            if (enabledOnly) {
                queries.push(Query.equal('enabled', true));
            }
            
            const response = await this.databases.listDocuments(
                this.databaseId,
                this.collections.events,
                queries
            );
            
            return response.documents.map(doc => this.mapEventDocument(doc));
        } catch (error) {
            databaseLogger.error('❌ Fehler beim Abrufen der Events', {
                error: error.message
            });
            throw error;
        }
    }
    
    /**
     * Event erstellen oder aktualisieren
     */
    async upsertEvent(eventData) {
        try {
            // Versuche existierendes Event zu finden
            const existing = await this.databases.listDocuments(
                this.databaseId,
                this.collections.events,
                [Query.equal('code', eventData.code)]
            );
            
            if (existing.documents.length > 0) {
                // Update
                const response = await this.databases.updateDocument(
                    this.databaseId,
                    this.collections.events,
                    existing.documents[0].$id,
                    eventData
                );
                return this.mapEventDocument(response);
            } else {
                // Create
                const response = await this.databases.createDocument(
                    this.databaseId,
                    this.collections.events,
                    'unique()',
                    eventData
                );
                return this.mapEventDocument(response);
            }
        } catch (error) {
            databaseLogger.error('❌ Fehler beim Event Upsert', {
                error: error.message,
                eventData
            });
            throw error;
        }
    }
    
    // ==================== REGIONS ====================
    
    /**
     * Regionen abrufen
     */
    async getRegions(enabledOnly = true) {
        try {
            const queries = [Query.orderAsc('name')];
            if (enabledOnly) {
                queries.push(Query.equal('enabled', true));
            }
            
            const response = await this.databases.listDocuments(
                this.databaseId,
                this.collections.regions,
                queries
            );
            
            return response.documents.map(doc => this.mapRegionDocument(doc));
        } catch (error) {
            databaseLogger.error('❌ Fehler beim Abrufen der Regionen', {
                error: error.message
            });
            throw error;
        }
    }
    
    /**
     * Region erstellen oder aktualisieren
     */
    async upsertRegion(regionData) {
        try {
            // Versuche existierende Region zu finden
            const existing = await this.databases.listDocuments(
                this.databaseId,
                this.collections.regions,
                [Query.equal('regionId', regionData.id)]
            );
            
            const appwriteData = {
                regionId: regionData.id,
                name: regionData.name,
                abbreviation: regionData.abbreviation,
                enabled: regionData.enabled
            };
            
            if (existing.documents.length > 0) {
                // Update
                const response = await this.databases.updateDocument(
                    this.databaseId,
                    this.collections.regions,
                    existing.documents[0].$id,
                    appwriteData
                );
                return this.mapRegionDocument(response);
            } else {
                // Create
                const response = await this.databases.createDocument(
                    this.databaseId,
                    this.collections.regions,
                    'unique()',
                    appwriteData
                );
                return this.mapRegionDocument(response);
            }
        } catch (error) {
            databaseLogger.error('❌ Fehler beim Region Upsert', {
                error: error.message,
                regionData
            });
            throw error;
        }
    }
    
    // ==================== RANKINGS ====================
    
    /**
     * Rankings abrufen
     */
    async getRankings(filters = {}) {
        try {
            const queries = [
                Query.orderAsc('timeSeconds'),
                Query.limit(filters.take || 100)
            ];
            
            if (filters.skip) {
                queries.push(Query.offset(filters.skip));
            }
            
            if (filters.eventId) {
                queries.push(Query.equal('eventId', filters.eventId));
            }
            
            if (filters.regionId) {
                queries.push(Query.equal('regionId', filters.regionId));
            }
            
            if (filters.ageGroup) {
                queries.push(Query.equal('ageGroup', filters.ageGroup));
            }
            
            if (filters.gender) {
                queries.push(Query.equal('gender', filters.gender));
            }
            
            if (filters.season) {
                queries.push(Query.equal('season', filters.season));
            }
            
            const response = await this.databases.listDocuments(
                this.databaseId,
                this.collections.rankings,
                queries
            );
            
            // Erweitere Rankings mit Event- und Region-Informationen
            const rankings = await Promise.all(
                response.documents.map(async (doc) => {
                    const ranking = this.mapRankingDocument(doc);
                    
                    // Event-Informationen laden
                    try {
                        const eventResponse = await this.databases.getDocument(
                            this.databaseId,
                            this.collections.events,
                            ranking.eventId
                        );
                        ranking.event = this.mapEventDocument(eventResponse);
                    } catch (error) {
                        ranking.event = { name: 'Unbekanntes Event' };
                    }
                    
                    // Region-Informationen laden
                    try {
                        const regionResponse = await this.databases.listDocuments(
                            this.databaseId,
                            this.collections.regions,
                            [Query.equal('regionId', ranking.regionId)]
                        );
                        
                        if (regionResponse.documents.length > 0) {
                            ranking.region = this.mapRegionDocument(regionResponse.documents[0]);
                        } else {
                            ranking.region = { name: 'Unbekannte Region' };
                        }
                    } catch (error) {
                        ranking.region = { name: 'Unbekannte Region' };
                    }
                    
                    return ranking;
                })
            );
            
            return rankings;
        } catch (error) {
            databaseLogger.error('❌ Fehler beim Abrufen der Rankings', {
                error: error.message,
                filters
            });
            throw error;
        }
    }
    
    /**
     * Mehrere Rankings erstellen
     */
    async createManyRankings(rankingsData) {
        try {
            const results = await Promise.all(
                rankingsData.map(async (rankingData) => {
                    try {
                        const appwriteData = {
                            swimmerName: rankingData.swimmerName,
                            birthYear: rankingData.birthYear,
                            club: rankingData.club,
                            time: rankingData.time,
                            timeSeconds: rankingData.timeSeconds,
                            rank: rankingData.rank,
                            ageGroup: rankingData.ageGroup,
                            gender: rankingData.gender,
                            competition: rankingData.competition,
                            date: rankingData.date ? rankingData.date.toISOString() : null,
                            location: rankingData.location,
                            eventId: rankingData.eventId,
                            regionId: rankingData.regionId,
                            season: rankingData.season,
                            scrapedAt: new Date().toISOString()
                        };
                        
                        return await this.databases.createDocument(
                            this.databaseId,
                            this.collections.rankings,
                            'unique()',
                            appwriteData
                        );
                    } catch (error) {
                        databaseLogger.warn('⚠️ Fehler beim Erstellen eines Rankings', {
                            error: error.message,
                            ranking: rankingData.swimmerName
                        });
                        return null;
                    }
                })
            );
            
            const successCount = results.filter(r => r !== null).length;
            
            return { count: successCount };
        } catch (error) {
            databaseLogger.error('❌ Fehler beim Erstellen der Rankings', {
                error: error.message
            });
            throw error;
        }
    }
    
    /**
     * Rankings für Event löschen
     */
    async deleteRankingsForEvent(eventId, regionId, ageGroup, gender, season) {
        try {
            const response = await this.databases.listDocuments(
                this.databaseId,
                this.collections.rankings,
                [
                    Query.equal('eventId', eventId),
                    Query.equal('regionId', regionId),
                    Query.equal('ageGroup', ageGroup),
                    Query.equal('gender', gender),
                    Query.equal('season', season)
                ]
            );
            
            const deletePromises = response.documents.map(doc =>
                this.databases.deleteDocument(
                    this.databaseId,
                    this.collections.rankings,
                    doc.$id
                )
            );
            
            await Promise.all(deletePromises);
            
            return { count: response.documents.length };
        } catch (error) {
            databaseLogger.error('❌ Fehler beim Löschen der Rankings', {
                error: error.message,
                eventId,
                regionId,
                ageGroup,
                gender,
                season
            });
            throw error;
        }
    }
    
    // ==================== MAPPING FUNCTIONS ====================
    
    mapEventDocument(doc) {
        return {
            id: doc.$id,
            name: doc.name,
            code: doc.code,
            distance: doc.distance,
            stroke: doc.stroke,
            course: doc.course,
            enabled: doc.enabled,
            priority: doc.priority,
            createdAt: new Date(doc.$createdAt),
            updatedAt: new Date(doc.$updatedAt)
        };
    }
    
    mapRegionDocument(doc) {
        return {
            id: doc.regionId,
            name: doc.name,
            abbreviation: doc.abbreviation,
            enabled: doc.enabled,
            createdAt: new Date(doc.$createdAt),
            updatedAt: new Date(doc.$updatedAt)
        };
    }
    
    mapRankingDocument(doc) {
        return {
            id: doc.$id,
            swimmerName: doc.swimmerName,
            birthYear: doc.birthYear,
            club: doc.club,
            time: doc.time,
            timeSeconds: doc.timeSeconds,
            rank: doc.rank,
            ageGroup: doc.ageGroup,
            gender: doc.gender,
            competition: doc.competition,
            date: doc.date ? new Date(doc.date) : null,
            location: doc.location,
            eventId: doc.eventId,
            regionId: doc.regionId,
            season: doc.season,
            scrapedAt: new Date(doc.scrapedAt),
            createdAt: new Date(doc.$createdAt),
            updatedAt: new Date(doc.$updatedAt)
        };
    }
    
    // ==================== STATISTICS ====================
    
    async getStatistics() {
        try {
            // Basis-Statistiken parallel abrufen
            const [rankingsResponse, eventsResponse, regionsResponse] = await Promise.all([
                this.databases.listDocuments(this.databaseId, this.collections.rankings, [Query.limit(1)]),
                this.databases.listDocuments(this.databaseId, this.collections.events, [Query.equal('enabled', true)]),
                this.databases.listDocuments(this.databaseId, this.collections.regions, [Query.equal('enabled', true)])
            ]);
            
            // Top Performers abrufen
            const topPerformersResponse = await this.databases.listDocuments(
                this.databaseId,
                this.collections.rankings,
                [Query.orderAsc('timeSeconds'), Query.limit(10)]
            );
            
            const topPerformers = await Promise.all(
                topPerformersResponse.documents.map(async (doc) => {
                    const ranking = this.mapRankingDocument(doc);
                    
                    // Event-Informationen laden
                    try {
                        const eventResponse = await this.databases.getDocument(
                            this.databaseId,
                            this.collections.events,
                            ranking.eventId
                        );
                        ranking.event = this.mapEventDocument(eventResponse);
                    } catch (error) {
                        ranking.event = { name: 'Unbekanntes Event' };
                    }
                    
                    return ranking;
                })
            );
            
            return {
                totalRankings: rankingsResponse.total,
                totalEvents: eventsResponse.total,
                totalRegions: regionsResponse.total,
                recentScrapeLogs: 0, // TODO: Implementieren wenn benötigt
                topPerformers,
                lastUpdated: new Date()
            };
        } catch (error) {
            databaseLogger.error('❌ Fehler beim Abrufen der Statistiken', {
                error: error.message
            });
            throw error;
        }
    }
}

export default AppwriteDatabaseManager;

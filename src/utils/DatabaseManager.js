/**
 * Database Manager für DSV Scraper - Node.js Version
 */
import { PrismaClient } from '@prisma/client';
import config from '../config.js';
import { databaseLogger } from './logger.js';

class DatabaseManager {
    constructor() {
        this.prisma = new PrismaClient({
            log: config.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
            datasources: {
                db: {
                    url: config.getDatabaseUrl()
                }
            }
        });
        
        this.isConnected = false;
        this.connectionRetries = 0;
        this.maxRetries = 5;
    }
    
    /**
     * Verbindung zur Datenbank herstellen
     */
    async connect() {
        try {
            await this.prisma.$connect();
            this.isConnected = true;
            this.connectionRetries = 0;
            databaseLogger.info('✅ Datenbankverbindung hergestellt');
            return true;
        } catch (error) {
            this.connectionRetries++;
            databaseLogger.error('❌ Datenbankverbindung fehlgeschlagen', {
                error: error.message,
                retries: this.connectionRetries
            });
            
            if (this.connectionRetries < this.maxRetries) {
                databaseLogger.info(`🔄 Wiederhole Verbindungsversuch in 5 Sekunden...`);
                await new Promise(resolve => setTimeout(resolve, 5000));
                return this.connect();
            }
            
            throw error;
        }
    }
    
    /**
     * Verbindung zur Datenbank trennen
     */
    async disconnect() {
        try {
            await this.prisma.$disconnect();
            this.isConnected = false;
            databaseLogger.info('🔌 Datenbankverbindung getrennt');
        } catch (error) {
            databaseLogger.error('❌ Fehler beim Trennen der Datenbankverbindung', {
                error: error.message
            });
        }
    }
    
    /**
     * Prüft ob die Datenbank erreichbar ist
     */
    async healthCheck() {
        try {
            await this.prisma.$queryRaw`SELECT 1`;
            return { status: 'healthy', timestamp: new Date() };
        } catch (error) {
            return { 
                status: 'unhealthy', 
                error: error.message, 
                timestamp: new Date() 
            };
        }
    }
    
    // ==================== EVENTS ====================
    
    /**
     * Alle Events abrufen
     */
    async getEvents(enabledOnly = true) {
        // enabledOnly Parameter wird ignoriert, da enabled Feld entfernt wurde
        return await this.prisma.event.findMany({
            orderBy: { name: 'asc' }
        });
    }
    
    /**
     * Event erstellen oder aktualisieren
     */
    async upsertEvent(eventData) {
        // Versuche zuerst, das Event zu finden (mit neuer unique constraint)
        const existingEvent = await this.prisma.event.findFirst({
            where: {
                code: eventData.code,
                gender: eventData.gender,
                ageGroup: eventData.ageGroup
            }
        });

        if (existingEvent) {
            // Event aktualisieren
            return await this.prisma.event.update({
                where: { id: existingEvent.id },
                data: eventData
            });
        } else {
            // Neues Event erstellen
            return await this.prisma.event.create({
                data: eventData
            });
        }
    }
    
    // ==================== REGIONS ====================
    
    /**
     * Alle Regionen abrufen
     */
    async getRegions(enabledOnly = true) {
        const where = enabledOnly ? { enabled: true } : {};
        return await this.prisma.region.findMany({
            where,
            orderBy: { name: 'asc' }
        });
    }
    
    /**
     * Region erstellen oder aktualisieren
     */
    async upsertRegion(regionData) {
        return await this.prisma.region.upsert({
            where: { id: regionData.id },
            update: regionData,
            create: regionData
        });
    }
    
    // ==================== RANKINGS ====================
    
    /**
     * Rankings abrufen mit Filtern
     */
    async getRankings(filters = {}) {
        const where = {};
        
        if (filters.eventId) where.eventId = filters.eventId;
        if (filters.regionId) where.regionId = filters.regionId;
        if (filters.ageGroup) where.ageGroup = filters.ageGroup;
        if (filters.gender) where.gender = filters.gender;
        if (filters.season) where.season = filters.season;
        if (filters.limit) filters.take = filters.limit;
        
        return await this.prisma.ranking.findMany({
            where,
            include: {
                event: true,
                region: true
            },
            orderBy: { timeSeconds: 'asc' },
            take: filters.take || 100
        });
    }
    
    /**
     * Ranking erstellen
     */
    async createRanking(rankingData) {
        return await this.prisma.ranking.create({
            data: rankingData
        });
    }
    
    /**
     * Mehrere Rankings erstellen
     */
    async createManyRankings(rankingsData) {
        return await this.prisma.ranking.createMany({
            data: rankingsData
        });
    }
    
    /**
     * Rankings für ein Event löschen (vor neuem Scraping)
     */
    async deleteRankingsForEvent(eventId, regionId, ageGroup, gender, season) {
        return await this.prisma.ranking.deleteMany({
            where: {
                eventId,
                regionId,
                ageGroup,
                gender,
                season
            }
        });
    }
    
    // ==================== SCRAPE LOGS ====================
    
    /**
     * Scrape Log erstellen
     */
    async createScrapeLog(logData) {
        return await this.prisma.scrapeLog.create({
            data: logData
        });
    }
    
    /**
     * Scrape Log aktualisieren
     */
    async updateScrapeLog(id, updateData) {
        return await this.prisma.scrapeLog.update({
            where: { id },
            data: updateData
        });
    }
    
    /**
     * Letzte Scrape Logs abrufen
     */
    async getRecentScrapeLogs(limit = 50) {
        return await this.prisma.scrapeLog.findMany({
            orderBy: { startTime: 'desc' },
            take: limit
        });
    }
    
    // ==================== SCHEDULER JOBS ====================
    
    /**
     * Scheduler Job erstellen oder aktualisieren
     */
    async upsertSchedulerJob(jobData) {
        return await this.prisma.schedulerJob.upsert({
            where: { name: jobData.name },
            update: jobData,
            create: jobData
        });
    }
    
    /**
     * Scheduler Job Status aktualisieren
     */
    async updateSchedulerJobStatus(name, status, duration = null, error = null) {
        const updateData = {
            lastRun: new Date(),
            lastStatus: status,
            totalRuns: { increment: 1 }
        };
        
        if (duration) updateData.lastDuration = duration;
        if (error) updateData.lastError = error;
        
        if (status === 'success') {
            updateData.successRuns = { increment: 1 };
        } else if (status === 'error') {
            updateData.errorRuns = { increment: 1 };
        }
        
        return await this.prisma.schedulerJob.update({
            where: { name },
            data: updateData
        });
    }
    
    // ==================== STATISTICS ====================
    
    /**
     * Statistiken abrufen
     */
    async getStatistics() {
        const [
            totalRankings,
            totalEvents,
            totalRegions,
            recentScrapeLogs,
            topPerformers
        ] = await Promise.all([
            this.prisma.ranking.count(),
            this.prisma.event.count(),
            this.prisma.region.count({ where: { enabled: true } }),
            this.prisma.scrapeLog.count({
                where: {
                    startTime: {
                        gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Letzte 7 Tage
                    }
                }
            }),
            this.prisma.ranking.findMany({
                take: 10,
                orderBy: { timeSeconds: 'asc' },
                include: {
                    event: true,
                    region: true
                }
            })
        ]);
        
        return {
            totalRankings,
            totalEvents,
            totalRegions,
            recentScrapeLogs,
            topPerformers,
            lastUpdated: new Date()
        };
    }
    
    // ==================== UTILITY METHODS ====================
    
    /**
     * Transaktion ausführen
     */
    async transaction(callback) {
        return await this.prisma.$transaction(callback);
    }
    
    /**
     * Raw Query ausführen
     */
    async rawQuery(query, params = []) {
        return await this.prisma.$queryRawUnsafe(query, ...params);
    }
    
    /**
     * Datenbank-Schema aktualisieren
     */
    async migrate() {
        // In Production sollte dies über Prisma CLI gemacht werden
        if (config.NODE_ENV !== 'production') {
            const { execSync } = await import('child_process');
            execSync('npx prisma db push', { stdio: 'inherit' });
        }
    }
}

export default DatabaseManager;

/**
 * Data Processor für DSV Scraper - Node.js Version
 */
import { databaseLogger } from './logger.js';

export class DataProcessor {
    constructor(databaseManager) {
        this.db = databaseManager;
    }
    
    /**
     * Scraping-Ergebnisse verarbeiten und in Datenbank speichern
     */
    async processScrapingResults(scrapingResults) {
        const processedResults = {};
        const startTime = Date.now();
        
        try {
            databaseLogger.info('🔄 Starte Datenverarbeitung', {
                totalEvents: Object.keys(scrapingResults).length
            });
            
            for (const [eventKey, rankings] of Object.entries(scrapingResults)) {
                try {
                    const processed = await this.processEventResults(eventKey, rankings);
                    processedResults[eventKey] = processed;
                } catch (error) {
                    databaseLogger.error('❌ Fehler bei Event-Verarbeitung', {
                        eventKey,
                        error: error.message
                    });
                    processedResults[eventKey] = { success: false, error: error.message };
                }
            }
            
            const duration = Date.now() - startTime;
            databaseLogger.info('✅ Datenverarbeitung abgeschlossen', {
                duration: `${duration}ms`,
                processedEvents: Object.keys(processedResults).length
            });
            
            return processedResults;
            
        } catch (error) {
            databaseLogger.error('❌ Fehler bei Datenverarbeitung', {
                error: error.message
            });
            throw error;
        }
    }
    
    /**
     * Ergebnisse für ein einzelnes Event verarbeiten
     */
    async processEventResults(eventKey, rankings) {
        if (!rankings || rankings.length === 0) {
            return { success: true, saved: 0, skipped: 0 };
        }
        
        try {
            // Event-Informationen aus dem ersten Ranking extrahieren
            const firstRanking = rankings[0];
            const eventInfo = this.extractEventInfo(eventKey, firstRanking);
            
            // Event in Datenbank sicherstellen
            const event = await this.ensureEvent(eventInfo);
            
            // Region sicherstellen
            const region = await this.ensureRegion(eventInfo.regionId);
            
            // Alte Rankings für dieses Event löschen
            await this.db.deleteRankingsForEvent(
                event.id,
                eventInfo.regionId,
                eventInfo.ageGroup,
                eventInfo.gender,
                eventInfo.season
            );
            
            // Neue Rankings vorbereiten
            const rankingsData = rankings.map(ranking => ({
                swimmerName: ranking.swimmerName,
                birthYear: ranking.birthYear,
                club: ranking.club,
                time: ranking.time,
                timeSeconds: ranking.timeSeconds,
                rank: ranking.rank,
                ageGroup: ranking.ageGroup,
                gender: ranking.gender,
                competition: ranking.competition,
                date: ranking.date,
                location: ranking.location,
                eventId: event.id,
                regionId: eventInfo.regionId,
                season: eventInfo.season
            }));
            
            // Rankings in Datenbank speichern
            const result = await this.db.createManyRankings(rankingsData);
            
            databaseLogger.info('💾 Rankings gespeichert', {
                eventKey,
                saved: result.count || rankingsData.length
            });
            
            return {
                success: true,
                saved: result.count || rankingsData.length,
                skipped: 0
            };
            
        } catch (error) {
            databaseLogger.error('❌ Fehler beim Speichern der Rankings', {
                eventKey,
                error: error.message
            });
            throw error;
        }
    }
    
    /**
     * Event-Informationen aus eventKey und Ranking extrahieren
     */
    extractEventInfo(eventKey, ranking) {
        // eventKey Format: "EventName_AgeGroup_Gender_RegionId"
        const parts = eventKey.split('_');
        
        return {
            eventName: parts[0] || ranking.eventName || 'Unknown Event',
            ageGroup: parts[1] || ranking.ageGroup,
            gender: parts[2] || ranking.gender,
            regionId: parseInt(parts[3]) || 1,
            season: ranking.season || '2025'
        };
    }
    
    /**
     * Event in Datenbank sicherstellen
     */
    async ensureEvent(eventInfo) {
        try {
            // Versuche Event anhand des Namens und Geschlechts zu finden
            const existingEvent = await this.db.prisma.event.findFirst({
                where: {
                    name: eventInfo.eventName,
                    gender: eventInfo.gender
                }
            });

            if (existingEvent) {
                return existingEvent;
            }

            // Event erstellen wenn nicht vorhanden
            const eventData = {
                name: eventInfo.eventName,
                code: this.generateEventCode(eventInfo.eventName, eventInfo.gender),
                distance: this.extractDistance(eventInfo.eventName),
                stroke: this.extractStroke(eventInfo.eventName),
                course: 'L', // Standard: Langbahn
                gender: eventInfo.gender
            };
            
            return await this.db.upsertEvent(eventData);
            
        } catch (error) {
            databaseLogger.error('❌ Fehler beim Event-Management', {
                eventInfo,
                error: error.message
            });
            throw error;
        }
    }
    
    /**
     * Region in Datenbank sicherstellen
     */
    async ensureRegion(regionId) {
        try {
            const existingRegion = await this.db.prisma.region.findUnique({
                where: { id: regionId }
            });
            
            if (existingRegion) {
                return existingRegion;
            }
            
            // Standard-Region erstellen wenn nicht vorhanden
            const regionData = {
                id: regionId,
                name: `Region ${regionId}`,
                abbreviation: `R${regionId}`
            };
            
            return await this.db.upsertRegion(regionData);
            
        } catch (error) {
            databaseLogger.error('❌ Fehler beim Region-Management', {
                regionId,
                error: error.message
            });
            throw error;
        }
    }
    
    /**
     * Event-Code generieren
     */
    generateEventCode(eventName, gender = null) {
        // Extrahiere Distanz und Schwimmart
        const distance = this.extractDistance(eventName);
        const stroke = this.extractStroke(eventName);

        const strokeCodes = {
            'Freistil': 'F',
            'Brust': 'B',
            'Rücken': 'R',
            'Schmetterling': 'S',
            'Lagen': 'L'
        };

        const strokeCode = strokeCodes[stroke] || 'X';
        return `${distance}${strokeCode}|GL`;
    }
    
    /**
     * Distanz aus Event-Name extrahieren
     */
    extractDistance(eventName) {
        const match = eventName.match(/(\d+)m/);
        return match ? parseInt(match[1]) : 0;
    }
    
    /**
     * Schwimmart aus Event-Name extrahieren
     */
    extractStroke(eventName) {
        const strokes = ['Freistil', 'Brust', 'Rücken', 'Schmetterling', 'Lagen'];
        
        for (const stroke of strokes) {
            if (eventName.includes(stroke)) {
                return stroke;
            }
        }
        
        return 'Unbekannt';
    }
    
    /**
     * Daten-Validierung
     */
    validateRankingData(ranking) {
        const errors = [];
        
        if (!ranking.swimmerName || ranking.swimmerName.trim().length === 0) {
            errors.push('Schwimmername ist erforderlich');
        }
        
        if (!ranking.time || ranking.time.trim().length === 0) {
            errors.push('Zeit ist erforderlich');
        }
        
        if (!ranking.timeSeconds || ranking.timeSeconds <= 0) {
            errors.push('Zeit in Sekunden muss größer als 0 sein');
        }
        
        if (!ranking.rank || ranking.rank <= 0) {
            errors.push('Rang muss größer als 0 sein');
        }
        
        if (!ranking.ageGroup) {
            errors.push('Altersgruppe ist erforderlich');
        }
        
        if (!ranking.gender || !['M', 'W'].includes(ranking.gender)) {
            errors.push('Geschlecht muss M oder W sein');
        }
        
        return errors;
    }
    
    /**
     * Daten bereinigen
     */
    cleanRankingData(ranking) {
        return {
            ...ranking,
            swimmerName: ranking.swimmerName?.trim(),
            club: ranking.club?.trim() || 'Unbekannt',
            time: ranking.time?.trim(),
            competition: ranking.competition?.trim() || null,
            location: ranking.location?.trim() || null
        };
    }
}

export default DataProcessor;

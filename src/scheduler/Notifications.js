/**
 * Notification Service für DSV Scraper - Node.js Version
 */
import nodemailer from 'nodemailer';
import axios from 'axios';
import config from '../config.js';
import { schedulerLogger } from '../utils/logger.js';

export class NotificationService {
    constructor() {
        this.emailTransporter = null;
        this.setupEmailTransporter();
    }
    
    /**
     * Email-Transporter konfigurieren
     */
    setupEmailTransporter() {
        if (!config.EMAIL_SMTP_SERVER || !config.EMAIL_USERNAME || !config.EMAIL_PASSWORD) {
            schedulerLogger.warn('⚠️ Email-Konfiguration unvollständig. Email-Benachrichtigungen deaktiviert.');
            return;
        }
        
        try {
            this.emailTransporter = nodemailer.createTransporter({
                host: config.EMAIL_SMTP_SERVER,
                port: config.EMAIL_SMTP_PORT,
                secure: config.EMAIL_SMTP_PORT === 465,
                auth: {
                    user: config.EMAIL_USERNAME,
                    pass: config.EMAIL_PASSWORD
                }
            });
            
            schedulerLogger.info('📧 Email-Transporter konfiguriert');
        } catch (error) {
            schedulerLogger.error('❌ Fehler bei Email-Transporter Konfiguration', {
                error: error.message
            });
        }
    }
    
    /**
     * Erfolgreiche Scraping-Benachrichtigung senden
     */
    async sendScrapingSuccessNotification(stats, duration) {
        const subject = '✅ DSV Scraping erfolgreich abgeschlossen';
        const message = this.formatScrapingSuccessMessage(stats, duration);
        
        await this.sendNotification({
            type: 'success',
            subject,
            message,
            priority: 'normal',
            category: 'scraping'
        });
    }
    
    /**
     * Scraping-Fehler Benachrichtigung senden
     */
    async sendScrapingErrorNotification(error, duration) {
        const subject = '❌ DSV Scraping fehlgeschlagen';
        const message = this.formatScrapingErrorMessage(error, duration);
        
        await this.sendNotification({
            type: 'error',
            subject,
            message,
            priority: 'high',
            category: 'scraping'
        });
    }
    
    /**
     * Gesundheits-Alert senden
     */
    async sendHealthAlertNotification(healthStatus) {
        const subject = '🚨 DSV Scraper Gesundheits-Alert';
        const message = this.formatHealthAlertMessage(healthStatus);
        
        await this.sendNotification({
            type: 'alert',
            subject,
            message,
            priority: 'urgent',
            category: 'system'
        });
    }
    
    /**
     * Allgemeine Benachrichtigung senden
     */
    async sendNotification({ type, subject, message, priority = 'normal', category = 'general' }) {
        const notifications = [];
        
        // Email-Benachrichtigung
        if (this.emailTransporter && config.NOTIFICATION_EMAIL) {
            notifications.push(this.sendEmailNotification(subject, message));
        }
        
        // Slack-Benachrichtigung
        if (config.SLACK_WEBHOOK_URL) {
            notifications.push(this.sendSlackNotification(subject, message, type));
        }
        
        // Alle Benachrichtigungen parallel senden
        const results = await Promise.allSettled(notifications);
        
        // Ergebnisse loggen
        results.forEach((result, index) => {
            const notificationType = index === 0 ? 'Email' : 'Slack';
            
            if (result.status === 'fulfilled') {
                schedulerLogger.info(`✅ ${notificationType}-Benachrichtigung gesendet`, {
                    subject,
                    type,
                    priority
                });
            } else {
                schedulerLogger.error(`❌ ${notificationType}-Benachrichtigung fehlgeschlagen`, {
                    subject,
                    error: result.reason?.message
                });
            }
        });
    }
    
    /**
     * Email-Benachrichtigung senden
     */
    async sendEmailNotification(subject, message) {
        if (!this.emailTransporter) {
            throw new Error('Email-Transporter nicht konfiguriert');
        }
        
        const mailOptions = {
            from: config.EMAIL_USERNAME,
            to: config.NOTIFICATION_EMAIL,
            subject: `[DSV Scraper] ${subject}`,
            text: message,
            html: this.formatEmailHTML(subject, message)
        };
        
        return await this.emailTransporter.sendMail(mailOptions);
    }
    
    /**
     * Slack-Benachrichtigung senden
     */
    async sendSlackNotification(subject, message, type) {
        if (!config.SLACK_WEBHOOK_URL) {
            throw new Error('Slack Webhook URL nicht konfiguriert');
        }
        
        const color = this.getSlackColor(type);
        const payload = {
            text: `[DSV Scraper] ${subject}`,
            attachments: [
                {
                    color,
                    text: message,
                    footer: 'DSV Scraper',
                    ts: Math.floor(Date.now() / 1000)
                }
            ]
        };
        
        return await axios.post(config.SLACK_WEBHOOK_URL, payload);
    }
    
    /**
     * Scraping-Erfolg Nachricht formatieren
     */
    formatScrapingSuccessMessage(stats, duration) {
        return `
Scraping erfolgreich abgeschlossen!

📊 Statistiken:
• Gesamte Events: ${stats.totalEvents}
• Erfolgreiche Events: ${stats.successfulEvents}
• Fehlgeschlagene Events: ${stats.failedEvents}
• Gesamte Rankings: ${stats.totalRankings}
• Regionen: ${stats.regions}
• Altersgruppen: ${stats.ageGroups}
• Geschlechter: ${stats.genders}

⏱️ Dauer: ${Math.round(duration / 1000)} Sekunden

Zeitstempel: ${new Date().toLocaleString('de-DE')}
        `.trim();
    }
    
    /**
     * Scraping-Fehler Nachricht formatieren
     */
    formatScrapingErrorMessage(error, duration) {
        return `
Scraping fehlgeschlagen!

❌ Fehler: ${error.message}

⏱️ Dauer bis Fehler: ${Math.round(duration / 1000)} Sekunden

🔍 Details:
${error.stack || 'Keine weiteren Details verfügbar'}

Zeitstempel: ${new Date().toLocaleString('de-DE')}

Bitte prüfen Sie die Logs für weitere Informationen.
        `.trim();
    }
    
    /**
     * Gesundheits-Alert Nachricht formatieren
     */
    formatHealthAlertMessage(healthStatus) {
        return `
System-Gesundheitsprüfung fehlgeschlagen!

🚨 Status: ${healthStatus.status}

${healthStatus.error ? `❌ Fehler: ${healthStatus.error}` : ''}

${healthStatus.details ? `📋 Details: ${JSON.stringify(healthStatus.details, null, 2)}` : ''}

Zeitstempel: ${new Date().toLocaleString('de-DE')}

Bitte prüfen Sie das System umgehend.
        `.trim();
    }
    
    /**
     * Email HTML formatieren
     */
    formatEmailHTML(subject, message) {
        return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>${subject}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f4f4f4;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .content {
            background-color: #fff;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .footer {
            margin-top: 20px;
            padding: 10px;
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        pre {
            background-color: #f8f8f8;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>🏊‍♂️ DSV Scraper Benachrichtigung</h2>
        <h3>${subject}</h3>
    </div>
    <div class="content">
        <pre>${message}</pre>
    </div>
    <div class="footer">
        Diese Nachricht wurde automatisch vom DSV Scraper System generiert.
    </div>
</body>
</html>
        `.trim();
    }
    
    /**
     * Slack-Farbe basierend auf Nachrichtentyp
     */
    getSlackColor(type) {
        const colors = {
            success: 'good',
            error: 'danger',
            warning: 'warning',
            alert: 'danger',
            info: '#36a64f'
        };
        
        return colors[type] || colors.info;
    }
}

export default NotificationService;

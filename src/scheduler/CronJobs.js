/**
 * Cron Jobs für automatisiertes Scraping - Node.js Version
 */
import cron from 'node-cron';
import config from '../config.js';
import { schedulerLogger, logSchedulerJob } from '../utils/logger.js';
import DatabaseManager from '../utils/DatabaseManager.js';
import DSVScraper from '../scrapers/DSVScraper.js';
import DataProcessor from '../utils/DataProcessor.js';
import NotificationService from './Notifications.js';

export class ScheduledScraper {
    constructor() {
        this.db = null;
        this.scraper = null;
        this.processor = null;
        this.notifications = null;
        this.isRunning = false;
        this.lastRunStats = {};
        this.scheduledJobs = new Map();
    }
    
    /**
     * Initialisierung
     */
    async initialize() {
        try {
            // Database Manager
            this.db = new DatabaseManager();
            await this.db.connect();
            
            // Scraper
            this.scraper = new DSVScraper(config.EVENTS_CONFIG_PATH);
            await this.scraper.loadConfig();
            
            // Data Processor
            this.processor = new DataProcessor(this.db);
            
            // Notification Service
            this.notifications = new NotificationService();
            
            schedulerLogger.info('✅ Scheduler initialisiert');
            
        } catch (error) {
            schedulerLogger.error('❌ Fehler bei Scheduler-Initialisierung', {
                error: error.message
            });
            throw error;
        }
    }
    
    /**
     * Alle Cron Jobs starten
     */
    async startAllJobs() {
        if (!config.ENABLE_SCHEDULER) {
            schedulerLogger.info('⏸️ Scheduler ist deaktiviert');
            return;
        }
        
        try {
            await this.initialize();
            
            // Wöchentliches Scraping
            this.scheduleWeeklyScraping();
            
            // Tägliche Bereinigung
            this.scheduleDailyCleanup();
            
            // Stündliche Gesundheitsprüfung
            this.scheduleHealthCheck();
            
            schedulerLogger.info('🚀 Alle Cron Jobs gestartet');
            
        } catch (error) {
            schedulerLogger.error('❌ Fehler beim Starten der Cron Jobs', {
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Manuelles Scraping ausführen
     */
    async runManualScraping(options = {}) {
        schedulerLogger.info('🔧 Manuelles Scraping gestartet', options);

        if (this.isRunning) {
            throw new Error('Scraping läuft bereits');
        }

        try {
            await this.initialize();
            return await this.weeklyScrapingJob();
        } catch (error) {
            schedulerLogger.error('❌ Manuelles Scraping fehlgeschlagen', { error: error.message });
            throw error;
        }
    }

    /**
     * Einzelnes Event scrapen
     */
    async runEventScraping(eventConfig) {
        schedulerLogger.info('🎯 Event-Scraping gestartet', eventConfig);

        try {
            await this.initialize();

            const startTime = Date.now();
            this.isRunning = true;

            // Event-spezifische Konfiguration erstellen
            const config = {
                eventName: eventConfig.eventName,
                eventCode: eventConfig.eventCode || `${eventConfig.eventName.replace(/\s+/g, '')}|GL`,
                ageGroup: eventConfig.ageGroup,
                gender: eventConfig.gender,
                regionId: eventConfig.regionId || 1,
                season: eventConfig.season || '2025',
                timeRange: eventConfig.timeRange || '01.06.2024|31.05.2025'
            };

            // Einzelnes Event scrapen
            const rankings = await this.scraper.scrapeEvent(config);

            // Ergebnisse verarbeiten
            let processed = { success: true, saved: 0 };
            if (rankings.length > 0) {
                const results = {
                    [`${config.eventName}_${config.ageGroup}_${config.gender}_${config.regionId}`]: rankings
                };
                const processedResults = await this.processor.processScrapingResults(results);
                processed = Object.values(processedResults)[0] || processed;
            }

            const duration = Date.now() - startTime;

            schedulerLogger.info('✅ Event-Scraping abgeschlossen', {
                event: config.eventName,
                ageGroup: config.ageGroup,
                gender: config.gender,
                regionId: config.regionId,
                rankingsFound: rankings.length,
                rankingsSaved: processed.saved,
                duration: `${Math.round(duration / 1000)}s`
            });

            return {
                success: true,
                event: config.eventName,
                ageGroup: config.ageGroup,
                gender: config.gender,
                regionId: config.regionId,
                rankingsFound: rankings.length,
                rankingsSaved: processed.saved,
                duration
            };

        } catch (error) {
            schedulerLogger.error('❌ Event-Scraping fehlgeschlagen', { error: error.message });
            throw error;
        } finally {
            this.isRunning = false;
        }
    }

    /**
     * Wöchentliches Scraping planen
     */
    scheduleWeeklyScraping() {
        // Cron Pattern für wöchentliches Scraping
        const [hour, minute] = config.SCRAPE_TIME.split(':');
        const dayOfWeek = this.getDayOfWeek(config.SCRAPE_DAY);
        const cronPattern = `${minute} ${hour} * * ${dayOfWeek}`;
        
        const job = cron.schedule(cronPattern, async () => {
            await this.weeklyScrapingJob();
        }, {
            scheduled: true,
            timezone: config.TIMEZONE
        });
        
        this.scheduledJobs.set('weekly_scraping', job);
        
        schedulerLogger.info('📅 Wöchentliches Scraping geplant', {
            pattern: cronPattern,
            nextRun: this.getNextRunTime(cronPattern)
        });
    }
    
    /**
     * Tägliche Bereinigung planen
     */
    scheduleDailyCleanup() {
        // Täglich um 02:00 Uhr
        const cronPattern = '0 2 * * *';
        
        const job = cron.schedule(cronPattern, async () => {
            await this.dailyCleanupJob();
        }, {
            scheduled: true,
            timezone: config.TIMEZONE
        });
        
        this.scheduledJobs.set('daily_cleanup', job);
        
        schedulerLogger.info('🧹 Tägliche Bereinigung geplant', {
            pattern: cronPattern
        });
    }
    
    /**
     * Stündliche Gesundheitsprüfung planen
     */
    scheduleHealthCheck() {
        // Jede Stunde zur vollen Stunde
        const cronPattern = '0 * * * *';
        
        const job = cron.schedule(cronPattern, async () => {
            await this.healthCheckJob();
        }, {
            scheduled: true,
            timezone: config.TIMEZONE
        });
        
        this.scheduledJobs.set('health_check', job);
        
        schedulerLogger.info('🏥 Gesundheitsprüfung geplant', {
            pattern: cronPattern
        });
    }
    
    /**
     * Wöchentliches Scraping durchführen
     */
    async weeklyScrapingJob() {
        if (this.isRunning) {
            schedulerLogger.warn('⚠️ Scraping läuft bereits, überspringe...');
            return;
        }
        
        const startTime = Date.now();
        this.isRunning = true;
        
        try {
            logSchedulerJob('weekly_scraping', 'started', 'Wöchentliches Scraping gestartet');
            
            // Scheduler Job in DB registrieren
            await this.db.upsertSchedulerJob({
                name: 'weekly_scraping',
                description: 'Wöchentliches Scraping aller Events',
                cronPattern: this.getCronPattern('weekly_scraping'),
                enabled: true,
                lastStatus: 'running'
            });
            
            // Scraping durchführen
            const results = await this.scraper.scrapeAllEvents();
            
            // Daten verarbeiten und speichern
            const processedResults = await this.processor.processScrapingResults(results);
            
            // Statistiken sammeln
            const stats = this.calculateStats(processedResults);
            this.lastRunStats = stats;
            
            const duration = Date.now() - startTime;
            
            // Erfolg in DB speichern
            await this.db.updateSchedulerJobStatus('weekly_scraping', 'success', duration);
            
            logSchedulerJob('weekly_scraping', 'completed', {
                duration: `${duration}ms`,
                stats
            });
            
            // Erfolgs-Benachrichtigung senden
            await this.notifications.sendScrapingSuccessNotification(stats, duration);
            
        } catch (error) {
            const duration = Date.now() - startTime;
            
            // Fehler in DB speichern
            await this.db.updateSchedulerJobStatus('weekly_scraping', 'error', duration, error.message);
            
            logSchedulerJob('weekly_scraping', 'failed', {
                error: error.message,
                duration: `${duration}ms`
            });
            
            // Fehler-Benachrichtigung senden
            await this.notifications.sendScrapingErrorNotification(error, duration);
            
        } finally {
            this.isRunning = false;
        }
    }
    
    /**
     * Tägliche Bereinigung durchführen
     */
    async dailyCleanupJob() {
        const startTime = Date.now();
        
        try {
            logSchedulerJob('daily_cleanup', 'started', 'Tägliche Bereinigung gestartet');
            
            // Alte Scrape Logs löschen (älter als 30 Tage)
            const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            const deletedLogs = await this.db.prisma.scrapeLog.deleteMany({
                where: {
                    startTime: {
                        lt: thirtyDaysAgo
                    }
                }
            });
            
            // Alte Benachrichtigungen löschen (älter als 7 Tage)
            const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
            const deletedNotifications = await this.db.prisma.notification.deleteMany({
                where: {
                    createdAt: {
                        lt: sevenDaysAgo
                    },
                    status: 'sent'
                }
            });
            
            const duration = Date.now() - startTime;
            
            logSchedulerJob('daily_cleanup', 'completed', {
                deletedLogs: deletedLogs.count,
                deletedNotifications: deletedNotifications.count,
                duration: `${duration}ms`
            });
            
        } catch (error) {
            const duration = Date.now() - startTime;
            
            logSchedulerJob('daily_cleanup', 'failed', {
                error: error.message,
                duration: `${duration}ms`
            });
        }
    }
    
    /**
     * Gesundheitsprüfung durchführen
     */
    async healthCheckJob() {
        try {
            const health = await this.db.healthCheck();
            
            if (health.status === 'unhealthy') {
                await this.notifications.sendHealthAlertNotification(health);
            }
            
            logSchedulerJob('health_check', health.status, health);
            
        } catch (error) {
            logSchedulerJob('health_check', 'failed', {
                error: error.message
            });
            
            await this.notifications.sendHealthAlertNotification({
                status: 'critical',
                error: error.message
            });
        }
    }
    
    /**
     * Alle Jobs stoppen
     */
    stopAllJobs() {
        this.scheduledJobs.forEach((job, name) => {
            job.destroy();
            schedulerLogger.info(`🛑 Job gestoppt: ${name}`);
        });
        
        this.scheduledJobs.clear();
        schedulerLogger.info('🔒 Alle Cron Jobs gestoppt');
    }
    
    /**
     * Hilfsmethoden
     */
    getDayOfWeek(dayName) {
        const days = {
            'sunday': 0,
            'monday': 1,
            'tuesday': 2,
            'wednesday': 3,
            'thursday': 4,
            'friday': 5,
            'saturday': 6
        };
        return days[dayName.toLowerCase()] || 0;
    }
    
    getCronPattern(jobName) {
        const job = this.scheduledJobs.get(jobName);
        return job ? job.options.cronExpression : null;
    }
    
    getNextRunTime(cronPattern) {
        try {
            const task = cron.schedule(cronPattern, () => {}, { scheduled: false });
            return task.nextDates(1)[0];
        } catch {
            return null;
        }
    }
    
    calculateStats(results) {
        const stats = {
            totalEvents: 0,
            totalRankings: 0,
            successfulEvents: 0,
            failedEvents: 0,
            regions: new Set(),
            ageGroups: new Set(),
            genders: new Set()
        };
        
        Object.entries(results).forEach(([key, rankings]) => {
            stats.totalEvents++;
            
            if (rankings.length > 0) {
                stats.successfulEvents++;
                stats.totalRankings += rankings.length;
                
                rankings.forEach(ranking => {
                    stats.ageGroups.add(ranking.ageGroup);
                    stats.genders.add(ranking.gender);
                });
            } else {
                stats.failedEvents++;
            }
        });
        
        return {
            ...stats,
            regions: stats.regions.size,
            ageGroups: stats.ageGroups.size,
            genders: stats.genders.size
        };
    }
}

export default ScheduledScraper;

#!/usr/bin/env node

/**
 * Test Script für einzelnes Event-Scraping
 * Testet ob die Parameter korrekt übertragen werden
 */

import { DSVScraper } from './src/scrapers/DSVScraper.js';

async function testSingleScrape() {
    console.log('🔍 Test: Einzelnes Event scrapen');
    console.log('=================================');
    
    try {
        // DSV Scraper initialisieren
        const scraper = new DSVScraper('scrapers/config/events.json');
        await scraper.loadConfig();
        
        console.log('✅ Scraper initialisiert');
        
        // Test 1: 200m Lagen, Männlich, Langbahn, Region Baden
        console.log('\n📋 Test 1: 200m Lagen, M, L, Baden');
        const config1 = {
            eventName: '200m Lagen',
            eventCode: '200L|GL',
            distance: 200,
            stroke: 'Lagen',
            course: 'L',
            gender: 'M',
            season: '2025',
            regionId: 1
        };
        
        console.log('Config:', {
            event: config1.eventCode,
            gender: config1.gender,
            course: config1.course,
            region: config1.regionId
        });
        
        const results1 = await scraper.scrapeEvent(config1);
        console.log(`✅ Ergebnis: ${results1.length} Rankings gefunden`);
        
        if (results1.length > 0) {
            console.log('Erste 3 Ergebnisse:');
            results1.slice(0, 3).forEach((result, i) => {
                console.log(`  ${i+1}. ${result.swimmerName} - ${result.time} (${result.club})`);
            });
        }
        
        // Test 2: 100m Freistil, Weiblich, Kurzbahn, Region Bayern
        console.log('\n📋 Test 2: 100m Freistil, W, S, Bayern');
        const config2 = {
            eventName: '100m Freistil',
            eventCode: '100F|GL',
            distance: 100,
            stroke: 'Freistil',
            course: 'S',
            gender: 'W',
            season: '2025',
            regionId: 2
        };
        
        console.log('Config:', {
            event: config2.eventCode,
            gender: config2.gender,
            course: config2.course,
            region: config2.regionId
        });
        
        const results2 = await scraper.scrapeEvent(config2);
        console.log(`✅ Ergebnis: ${results2.length} Rankings gefunden`);
        
        if (results2.length > 0) {
            console.log('Erste 3 Ergebnisse:');
            results2.slice(0, 3).forEach((result, i) => {
                console.log(`  ${i+1}. ${result.swimmerName} - ${result.time} (${result.club})`);
            });
        }
        
        // Test 3: 50m Brust, Männlich, Langbahn, Region Berlin
        console.log('\n📋 Test 3: 50m Brust, M, L, Berlin');
        const config3 = {
            eventName: '50m Brust',
            eventCode: '50B|GL',
            distance: 50,
            stroke: 'Brust',
            course: 'L',
            gender: 'M',
            season: '2025',
            regionId: 3
        };
        
        console.log('Config:', {
            event: config3.eventCode,
            gender: config3.gender,
            course: config3.course,
            region: config3.regionId
        });
        
        const results3 = await scraper.scrapeEvent(config3);
        console.log(`✅ Ergebnis: ${results3.length} Rankings gefunden`);
        
        if (results3.length > 0) {
            console.log('Erste 3 Ergebnisse:');
            results3.slice(0, 3).forEach((result, i) => {
                console.log(`  ${i+1}. ${result.swimmerName} - ${result.time} (${result.club})`);
            });
        }
        
        console.log('\n🎉 Alle Tests abgeschlossen!');
        
        // Zusammenfassung
        console.log('\n📊 Zusammenfassung:');
        console.log(`Test 1 (200L|GL, M, L, Baden): ${results1.length} Rankings`);
        console.log(`Test 2 (100F|GL, W, S, Bayern): ${results2.length} Rankings`);
        console.log(`Test 3 (50B|GL, M, L, Berlin): ${results3.length} Rankings`);
        
        if (results1.length > 0 && results2.length > 0 && results3.length > 0) {
            console.log('\n✅ SUCCESS: Alle Parameter werden korrekt übertragen!');
        } else {
            console.log('\n⚠️  Einige Tests haben keine Ergebnisse geliefert');
        }
        
    } catch (error) {
        console.error('\n❌ Fehler beim Test:', error.message);
        console.error(`   Stack:`, error.stack);
    }
}

// Führe Test aus
testSingleScrape().catch(console.error);

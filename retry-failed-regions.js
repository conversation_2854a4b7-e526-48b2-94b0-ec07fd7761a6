#!/usr/bin/env node

/**
 * Retry Script für fehlgeschlagene Regionen
 * Versucht nur die Regionen, die beim letzten Scraping mit 500-Fehlern fehlgeschlagen sind
 */

import { DSVScraper } from './src/scrapers/DSVScraper.js';
import { DataProcessor } from './src/utils/DataProcessor.js';
import DatabaseManager from './src/utils/DatabaseManager.js';

// Fehlgeschlagene Regionen vom letzten Scraping
const FAILED_REGIONS = [
    { id: 2, name: 'Berlin' },
    { id: 7, name: 'Hessen' },
    { id: 9, name: 'Mecklenburg-Vorpommern' },
    { id: 14, name: 'Schleswig-Holstein' },
    { id: 16, name: 'Th<PERSON>ringen' },
    { id: 11, name: 'Nordrhein-Westfalen' }
];

const COURSES = [
    { code: 'L', name: 'Langbahn' },
    { code: 'S', name: '<PERSON>rz<PERSON>' }
];

async function retryFailedRegions() {
    console.log('🔄 RETRY FEHLGESCHLAGENER REGIONEN');
    console.log('==================================');
    console.log(`📍 Regionen: ${FAILED_REGIONS.map(r => r.name).join(', ')}`);
    console.log(`🏊‍♂️ Event: 50m Schmetterling, männlich, alle Altersgruppen`);
    console.log(`📅 Saison: 2025\n`);
    
    let scraper, dataProcessor, dbManager;
    
    try {
        // Komponenten initialisieren
        console.log('📦 Initialisiere Komponenten...');
        
        dbManager = new DatabaseManager();
        await dbManager.connect();
        console.log('  ✅ Datenbank verbunden');
        
        dataProcessor = new DataProcessor(dbManager);
        console.log('  ✅ Data Processor bereit');
        
        scraper = new DSVScraper('scrapers/config/events.json');
        await scraper.loadConfig();
        console.log('  ✅ DSV Scraper initialisiert');
        
        // Base Event Configuration
        const baseEventConfig = {
            eventName: '50m Schmetterling',
            eventCode: '50S|GL',
            distance: 50,
            stroke: 'Schmetterling',
            gender: 'M',
            season: '2025',
            ageGroup: '-1|-1'
        };
        
        let totalResults = 0;
        let totalRegionsProcessed = 0;
        let totalErrors = 0;
        const allResults = [];
        const errorLog = [];
        
        console.log('\n🔄 STARTE RETRY-SCRAPING...');
        console.log('='.repeat(50));
        
        // Für jede fehlgeschlagene Region
        for (const region of FAILED_REGIONS) {
            console.log(`\n📍 Region: ${region.name} (ID: ${region.id})`);
            
            // Für jeden Kurs (Langbahn + Kurzbahn)
            for (const course of COURSES) {
                console.log(`\n  🏊‍♂️ ${course.name}:`);
                
                const config = {
                    ...baseEventConfig,
                    course: course.code === 'L' ? 'L' : 'S',
                    regionId: region.id
                };
                
                try {
                    // Längere Pause vor jedem Retry-Versuch
                    console.log('    ⏳ Warte 10 Sekunden vor Retry...');
                    await new Promise(resolve => setTimeout(resolve, 10000));
                    
                    const results = await scraper.scrapeEvent(config);
                    
                    if (results.length > 0) {
                        console.log(`    ✅ ${results.length} Rankings gefunden`);
                        
                        // Beste Zeit anzeigen
                        const bestTime = results[0];
                        console.log(`    🏆 Beste Zeit: ${bestTime.swimmerName} - ${bestTime.time} (${bestTime.club})`);
                        
                        // Daten sofort in Datenbank speichern
                        const eventKey = `${baseEventConfig.eventCode}_${baseEventConfig.gender}_ALL_${baseEventConfig.season}_${region.id}_${course.code}`;
                        await dataProcessor.processEventResults(eventKey, results.map(result => ({
                            ...result,
                            region: region.name,
                            regionId: region.id,
                            course: course.name,
                            courseCode: course.code,
                            eventName: baseEventConfig.eventName,
                            eventCode: baseEventConfig.eventCode,
                            distance: baseEventConfig.distance,
                            stroke: baseEventConfig.stroke,
                            gender: baseEventConfig.gender,
                            season: baseEventConfig.season,
                            ageGroup: '-1|-1',
                            scrapedAt: new Date()
                        })));
                        
                        console.log(`    💾 ${results.length} Rankings in Datenbank gespeichert`);
                        
                        totalResults += results.length;
                        totalRegionsProcessed++;
                        
                        // Zu Gesamtergebnissen hinzufügen
                        results.forEach(result => {
                            allResults.push({
                                ...result,
                                region: region.name,
                                regionId: region.id,
                                course: course.name,
                                courseCode: course.code,
                                eventName: baseEventConfig.eventName,
                                eventCode: baseEventConfig.eventCode,
                                distance: baseEventConfig.distance,
                                stroke: baseEventConfig.stroke,
                                gender: baseEventConfig.gender,
                                season: baseEventConfig.season,
                                ageGroup: '-1|-1',
                                scrapedAt: new Date()
                            });
                        });
                        
                    } else {
                        console.log('    ⚠️  Keine Rankings gefunden');
                    }
                    
                } catch (error) {
                    console.log(`    ❌ Retry-Fehler: ${error.message}`);
                    totalErrors++;
                    errorLog.push({
                        region: region.name,
                        course: course.name,
                        type: 'retry',
                        error: error.message
                    });
                }
                
                // Pause zwischen Kursen
                await new Promise(resolve => setTimeout(resolve, 3000));
            }
            
            // Längere Pause zwischen Regionen
            await new Promise(resolve => setTimeout(resolve, 5000));
        }
        
        console.log('\n🎉 RETRY ABGESCHLOSSEN!');
        console.log('='.repeat(50));
        console.log(`📊 Gesamt gefundene Rankings: ${totalResults}`);
        console.log(`✅ Erfolgreich verarbeitete Regionen: ${totalRegionsProcessed}`);
        console.log(`❌ Fehler: ${totalErrors}`);
        
        if (allResults.length > 0) {
            // Beste Zeiten anzeigen
            const sortedResults = allResults.sort((a, b) => {
                const timeA = parseFloat(a.time.replace(':', '').replace(',', '.'));
                const timeB = parseFloat(b.time.replace(':', '').replace(',', '.'));
                return timeA - timeB;
            });
            
            console.log('\n🏆 TOP 5 ZEITEN AUS RETRY:');
            sortedResults.slice(0, 5).forEach((result, i) => {
                console.log(`  ${i+1}. ${result.swimmerName} - ${result.time} (${result.club}, ${result.region})`);
            });
        }
        
        if (errorLog.length > 0) {
            console.log('\n❌ FEHLER-LOG:');
            errorLog.forEach(error => {
                console.log(`  - ${error.region} (${error.course}): ${error.error}`);
            });
        }
        
    } catch (error) {
        console.error('\n❌ Kritischer Fehler:', error.message);
        console.error('Stack:', error.stack);
    } finally {
        // Cleanup
        if (dbManager) {
            await dbManager.disconnect();
            console.log('\n📦 Datenbankverbindung geschlossen');
        }
    }
}

// Retry starten
retryFailedRegions().catch(console.error);

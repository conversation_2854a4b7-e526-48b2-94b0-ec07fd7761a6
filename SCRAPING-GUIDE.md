# DSV Scraper - Scraping Guide

Comprehensive guide for starting initial scraping with the DSV Rankings system.

## 🚀 **Method 1: Manual Scraping Script**

### Quick Start
```bash
# Run initial scraping with default settings
npm run scrape:initial

# Or run directly
node scripts/initial-scrape.js
```

### Advanced Usage
```bash
# Manual scraping with options
npm run scrape:manual

# Filter by specific event
node scripts/manual-scrape.js --event="200m Lagen"

# Filter by age group and gender
node scripts/manual-scrape.js --age=2015 --gender=M

# Dry run (don't save to database)
node scripts/manual-scrape.js --dry-run --verbose

# Filter by region
node scripts/manual-scrape.js --region=1 --verbose

# Show help
node scripts/manual-scrape.js --help
```

### Examples
```bash
# Scrape only 200m Lagen for 2015 boys
node scripts/manual-scrape.js --event="200m Lagen" --age=2015 --gender=M

# Test scraping without saving
node scripts/manual-scrape.js --dry-run --verbose

# Scrape specific region with detailed output
node scripts/manual-scrape.js --region=1 --verbose
```

## 🔄 **Method 2: API-Based Scraping**

### Start Manual Scraping via API
```bash
# Trigger full manual scraping
curl -X POST http://localhost:3000/api/scrape/manual

# Response:
# {
#   "success": true,
#   "message": "Manual scraping started",
#   "jobId": "manual-1642678901234",
#   "timestamp": "2024-01-20T10:15:01.234Z"
# }
```

### Scrape Single Event
```bash
# Scrape specific event
curl -X POST http://localhost:3000/api/scrape/event \
  -H "Content-Type: application/json" \
  -d '{
    "eventName": "200m Lagen",
    "ageGroup": "2015",
    "gender": "M",
    "regionId": 1
  }'

# Response:
# {
#   "success": true,
#   "message": "Event scraping completed",
#   "event": "200m Lagen",
#   "ageGroup": "2015",
#   "gender": "M",
#   "regionId": 1,
#   "rankingsFound": 25,
#   "rankingsSaved": 25,
#   "timestamp": "2024-01-20T10:15:01.234Z"
# }
```

### Check Scraping Status
```bash
# Get scraping status
curl http://localhost:3000/api/scrape/status

# Response:
# {
#   "success": true,
#   "status": {
#     "database": "healthy",
#     "lastScrape": {...},
#     "recentActivity": 5,
#     "scheduler": "disabled"
#   },
#   "recentLogs": [...]
# }
```

## 🛠️ **Method 3: Scheduler-Based Scraping**

### Enable Scheduler
```bash
# Update environment
echo "ENABLE_SCHEDULER=true" >> .env

# Start scheduler service
npm run scheduler
```

### Manual Trigger via Scheduler
```javascript
// In Node.js code
import { ScheduledScraper } from './src/scheduler/CronJobs.js';

const scheduler = new ScheduledScraper();

// Run manual scraping
await scheduler.runManualScraping();

// Run single event
await scheduler.runEventScraping({
  eventName: "200m Lagen",
  ageGroup: "2015",
  gender: "M",
  regionId: 1
});
```

## 📋 **Configuration**

### Events Configuration
Edit `src/scrapers/config/events.json`:

```json
{
  "events": [
    {
      "name": "200m Lagen",
      "code": "200L|GL",
      "distance": 200,
      "stroke": "Lagen",
      "course": "L",
      "enabled": true,
      "priority": 1
    }
  ],
  "age_groups": [
    {"year": "2015", "enabled": true}
  ],
  "genders": [
    {"code": "M", "enabled": true},
    {"code": "W", "enabled": true}
  ],
  "regions": [
    {"id": 1, "name": "Baden"}
  ]
}
```

### Environment Variables
```bash
# Scraping settings
SCRAPING_DELAY=1000          # Delay between requests (ms)
MAX_RETRIES=3                # Retry failed requests
TIMEOUT=30000                # Request timeout (ms)
PUPPETEER_HEADLESS=true      # Run browser headless

# Scheduler settings
ENABLE_SCHEDULER=false       # Enable/disable scheduler
SCRAPE_TIME=06:00           # Daily scraping time
SCRAPE_DAY=sunday           # Weekly scraping day
```

## 📊 **Monitoring Progress**

### Web Interface
- **Dashboard**: http://localhost:3000/
- **Rankings**: http://localhost:3000/rankings
- **Events**: http://localhost:3000/events
- **Statistics**: http://localhost:3000/statistics
- **Logs**: http://localhost:3000/logs

### Command Line
```bash
# Watch logs
tail -f logs/dsv-scraper.log

# Check database
npm run db:studio

# Health check
curl http://localhost:3000/health
```

## 🎯 **Recommended Workflow**

### First Time Setup
1. **Configure events** in `events.json`
2. **Start with one event** enabled
3. **Run initial scraping**:
   ```bash
   npm run scrape:initial
   ```
4. **Check results** in web interface
5. **Enable more events** gradually

### Regular Usage
1. **Use API endpoints** for manual triggers
2. **Enable scheduler** for automation
3. **Monitor via web interface**
4. **Export data** as needed

## 🔧 **Troubleshooting**

### Common Issues

**No rankings found:**
- Check if events are enabled in config
- Verify age groups have data in current season
- Check network connectivity

**Scraping fails:**
- Increase timeout in environment
- Check DSV website availability
- Verify event codes are correct

**Database errors:**
- Ensure database is running
- Check connection string
- Run `npm run db:push` to sync schema

### Debug Mode
```bash
# Run with verbose output
node scripts/manual-scrape.js --verbose

# Dry run to test without saving
node scripts/manual-scrape.js --dry-run --verbose

# Check specific event
node scripts/manual-scrape.js --event="200m Lagen" --verbose
```

## 📈 **Performance Tips**

1. **Start small**: Enable 1-2 events initially
2. **Use delays**: Set appropriate `SCRAPING_DELAY`
3. **Monitor resources**: Watch memory usage
4. **Batch processing**: Use age group filters
5. **Schedule wisely**: Run during off-peak hours

## 🔄 **Data Flow**

1. **Configuration** → Events, age groups, regions defined
2. **Scraping** → Data fetched from DSV website
3. **Processing** → Data cleaned and validated
4. **Storage** → Rankings saved to database
5. **Access** → Data available via web interface and API

## 📚 **Next Steps**

After successful initial scraping:
1. **Explore the data** in the web interface
2. **Set up regular scraping** with scheduler
3. **Export data** for analysis
4. **Configure notifications** for monitoring
5. **Extend events** to cover more disciplines

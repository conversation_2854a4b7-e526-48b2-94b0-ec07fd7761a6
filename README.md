# DSV Rankings - Node.js Version

Ein umfassendes System für das automatisierte Scraping und die Darstellung von DSV-Schwimmdaten, komplett in Node.js umgeschrieben.

## 🏊‍♂️ Features

- **Automatisiertes Scraping**: Wöchentliches Scraping von DSV-Daten mit Puppeteer
- **Express Web-Dashboard**: Moderne, responsive Web-Oberfläche
- **Prisma Database**: Type-safe Datenbankzugriff mit PostgreSQL/SQLite
- **Appwrite Integration**: Cloud-Deployment mit Appwrite Functions
- **Cron Scheduling**: Automatisierte Jobs mit node-cron
- **Comprehensive Testing**: Jest-basierte Test-Suite mit 70%+ Coverage
- **Email & Slack Notifications**: Benachrichtigungen bei Erfolg/Fehlern
- **CSV Export**: Datenexport für weitere Analysen

## 🚀 Schnellstart

### Voraussetzungen

- Node.js 18.0.0+
- npm 9.0.0+
- PostgreSQL (optional, SQLite als Alternative)

### Lokale Installation

1. **Repository klonen**
```bash
git clone <repository-url>
cd dsv-scraper
```

2. **Dependencies installieren**
```bash
npm install
```

3. **Environment-Datei erstellen**
```bash
cp .env.nodejs.example .env
# Bearbeite .env mit deinen Konfigurationen
```

4. **Datenbank initialisieren**
```bash
npm run db:generate
npm run db:push
```
5. **Server starten**
```bash
# Development
npm run dev

# Production
npm start
```

6. **Scheduler starten (optional)**
```bash
npm run scheduler
```

Die Anwendung ist dann unter `http://localhost:3000` verfügbar.

## 🧪 Testing

```bash
# Alle Tests ausführen
npm test

# Tests mit Coverage
npm test -- --coverage

# Watch Mode
npm run test:watch

# Vollständige Test-Suite
./scripts/test.sh
```

## 📋 Verfügbare Kommandos

### NPM Scripts

```bash
# Development
npm run dev              # Server mit Hot-Reload starten
npm start               # Production Server starten
npm run scheduler       # Scheduler starten

# Database
npm run db:generate     # Prisma Client generieren
npm run db:push         # Schema zur Datenbank pushen
npm run db:migrate      # Datenbank migrieren
npm run db:studio       # Prisma Studio öffnen

# Testing
npm test                # Alle Tests ausführen
npm run test:watch      # Tests im Watch-Mode
npm run lint            # Code linting
npm run lint:fix        # Linting-Fehler automatisch beheben
npm run format          # Code formatieren

# Utilities
./scripts/test.sh       # Vollständige Test-Suite
./scripts/cleanup.sh    # Projekt bereinigen
```

## 🏗️ Architektur

### Projektstruktur

```
dsv-scraper/
├── src/                    # Node.js Source Code
│   ├── config.js          # Zentrale Konfiguration
│   ├── index.js           # Web-App Entry Point
│   ├── scheduler.js       # Scheduler Entry Point
│   ├── web/               # Express Web Application
│   │   ├── app.js
│   │   ├── routes/
│   │   ├── views/
│   │   └── public/
│   ├── scrapers/          # Scraping-Module
│   │   ├── BaseScraper.js
│   │   ├── DSVScraper.js
│   │   └── config/
│   ├── utils/             # Hilfsfunktionen
│   │   ├── DatabaseManager.js
│   │   ├── DataProcessor.js
│   │   ├── AppwriteDatabase.js
│   │   └── logger.js
│   └── scheduler/         # Scheduling & Notifications
│       ├── CronJobs.js
│       └── Notifications.js
├── prisma/                # Prisma Database Schema
├── tests/                 # Umfassende Test-Suite
├── data/                  # Datenverzeichnisse
└── scripts/               # Utility Scripts
```

### Technologie-Stack

- **Web Framework**: Express.js
- **Database ORM**: Prisma
- **Scraping**: Puppeteer + Cheerio + Axios
- **Scheduling**: node-cron
- **Testing**: Jest + Supertest
- **Logging**: Winston
- **Templates**: EJS

## 🔧 Konfiguration

### Events konfigurieren

Events werden in `src/scrapers/config/events.json` konfiguriert:

```json
{
  "events": [
    {
      "name": "200m Lagen",
      "code": "200L|GL",
      "distance": 200,
      "stroke": "Lagen",
      "course": "L",
      "enabled": true,
      "priority": 1
    }
  ]
}
```

### Umgebungsvariablen

Wichtige Umgebungsvariablen in `.env`:

```bash
# Node.js Environment
NODE_ENV=development
PORT=3000

# Datenbank
DATABASE_TYPE=postgresql
DATABASE_URL=postgresql://user:pass@localhost:5432/dsv_rankings

# Scraping
SCRAPING_DELAY=1
MAX_RETRIES=3
TIMEOUT=30000

# Scheduling
ENABLE_SCHEDULER=true
SCRAPE_TIME=06:00
SCRAPE_DAY=sunday
TIMEZONE=Europe/Berlin

# Benachrichtigungen
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password
SLACK_WEBHOOK_URL=your-slack-webhook
```

## 📊 Web-Interface

### Dashboard
- Übersicht über alle Events
- Systemstatistiken
- Letzte Aktivitäten
- Schnellzugriff auf Funktionen

### Rankings
- Filterable Tabellen
- Schwimmer-Details
- Export-Funktionen
- Responsive Design

### Admin-Panel
- System-Monitoring
- Scheduler-Kontrolle
- Manuelle Aktionen
- Log-Anzeige

## 🔄 Erweiterungen

### Neue Strecken hinzufügen

1. **Über Web-Interface**: Admin-Panel → Events verwalten
2. **Über Konfiguration**: `src/scrapers/config/events.json` bearbeiten
3. **Über API**: POST Request an `/api/events`

### Neue Schwimmverbände

Erstellen Sie eine neue Scraper-Klasse:

```javascript
import { BaseScraper, SwimmerResult } from './BaseScraper.js';

export class OSVScraper extends BaseScraper {
    getFederationName() {
        return "Österreichischer Schwimmverband";
    }

    async scrapeEvent(config) {
        // OSV-spezifische Implementierung
        const results = [];
        // ... scraping logic
        return results;
    }
}
```

## 📈 Monitoring

### Logs

- **Application-Logs**: `logs/dsv-scraper.log`
- **Error-Logs**: `logs/error.log`
- **Scraping-Logs**: `logs/scraping.log`
- **Database-Logs**: In Datenbank gespeichert

### Metriken

- Anzahl erfolgreicher/fehlgeschlagener Scraping-Vorgänge
- Datenqualität und -integrität
- System-Performance und Memory-Usage
- API Response-Zeiten

### Benachrichtigungen

- E-Mail bei Fehlern und Erfolg
- Slack-Integration
- Wöchentliche Zusammenfassungen
- Health-Check Alerts

## 🧪 Tests

```bash
# Alle Tests ausführen
npm test

# Tests mit Coverage
npm test -- --coverage

# Spezifische Tests
npm test tests/config.test.js

# Watch Mode
npm run test:watch

# Vollständige Test-Suite
./scripts/test.sh
```

### Test-Kategorien

- **Unit Tests**: Einzelne Module und Funktionen
- **Integration Tests**: Vollständiger Scraping-Workflow
- **API Tests**: REST-Endpoints mit Supertest
- **Performance Tests**: Timing und Memory-Tests

## 🚀 Deployment

Das System ist für lokale Entwicklung optimiert. Für Production-Deployment können verschiedene Optionen genutzt werden:

### Lokale Entwicklung

```bash
# Development Server
npm run dev

# Production Build
npm start
```

### Produktions-Setup

1. **Umgebung vorbereiten**
```bash
# Produktions-Konfiguration
cp .env.nodejs.example .env.production
# Produktions-Werte eintragen

# Dependencies installieren
npm ci --only=production
```

2. **Datenbank vorbereiten**
```bash
# Prisma generieren
npm run db:generate

# Schema deployen
npm run db:push
```

3. **Process Manager (PM2)**
```bash
# PM2 installieren
npm install -g pm2

# Anwendung starten
pm2 start src/index.js --name "dsv-scraper"
pm2 start src/scheduler.js --name "dsv-scheduler"

# PM2 Monitoring
pm2 monit
```

### Backup-Strategie

```bash
# Datenbank-Backup (PostgreSQL)
pg_dump dsv_rankings > backup_$(date +%Y%m%d).sql

# SQLite Backup
cp dsv_rankings.db backup_$(date +%Y%m%d).db

# Logs-Backup
tar -czf logs_backup_$(date +%Y%m%d).tar.gz logs/
```

## 🤝 Beitragen

1. Fork des Repositories
2. Feature-Branch erstellen (`git checkout -b feature/neue-funktion`)
3. Dependencies installieren (`npm install`)
4. Tests ausführen (`npm test`)
5. Änderungen committen (`git commit -am 'Neue Funktion hinzugefügt'`)
6. Branch pushen (`git push origin feature/neue-funktion`)
7. Pull Request erstellen

### Development Guidelines

- Verwende ESLint und Prettier für Code-Formatierung
- Schreibe Tests für neue Features
- Halte die Test-Coverage über 70%
- Folge den Conventional Commits Standards

## 📝 Lizenz

Dieses Projekt steht unter der MIT-Lizenz. Siehe `LICENSE` für Details.

## 🆘 Support

Bei Fragen oder Problemen:

1. **Issues**: GitHub Issues für Bug-Reports und Feature-Requests
2. **Dokumentation**:
   - `README-NODEJS.md` - Detaillierte Node.js Dokumentation
   - `TESTING.md` - Umfassende Test-Anleitung
3. **Logs**: Prüfen Sie die Log-Dateien in `logs/` für Fehlerdiagnose
4. **Health Check**: `GET /health` für System-Status

## 🔮 Roadmap

### Version 1.1
- [ ] Kurzbahn-Unterstützung
- [ ] Weitere Strecken (50m, 400m, 800m, 1500m)
- [ ] Erweiterte Statistiken und Analytics
- [ ] Docker Container Support

### Version 1.2
- [ ] Andere Schwimmverbände (ÖSV, Swiss Swimming)
- [ ] GraphQL API
- [ ] Real-time Updates mit WebSockets
- [ ] Advanced Caching mit Redis

### Version 2.0
- [ ] Machine Learning für Leistungsprognosen
- [ ] Microservices-Architektur
- [ ] Cloud-native Deployment
- [ ] Mobile App mit React Native

---

**✅ Migration abgeschlossen**: Das DSV-Scraper Projekt wurde erfolgreich von Python auf Node.js umgeschrieben mit umfassender Test-Suite und moderner Architektur.

#!/usr/bin/env node

/**
 * Speichert die bereits gescrapten 50m Schmetterling Daten in die Datenbank
 * Basierend auf den Ergebnissen des vorherigen Scraping-Laufs
 */

import { DataProcessor } from './src/utils/DataProcessor.js';
import DatabaseManager from './src/utils/DatabaseManager.js';

// Hilfsfunktion: Zeit-String in Sekunden umwandeln
function timeToSeconds(timeString) {
    if (!timeString || typeof timeString !== 'string') return null;

    // Format: "0:35,71" oder "1:23,45"
    const parts = timeString.split(':');
    if (parts.length !== 2) return null;

    const minutes = parseInt(parts[0], 10);
    const secondsParts = parts[1].split(',');
    if (secondsParts.length !== 2) return null;

    const seconds = parseInt(secondsParts[0], 10);
    const hundredths = parseInt(secondsParts[1], 10);

    return minutes * 60 + seconds + hundredths / 100;
}

async function savePreviousResults() {
    console.log('💾 Speichere 50m Schmetterling Daten in Datenbank');
    console.log('================================================');
    
    let dataProcessor, dbManager;
    
    try {
        // Komponenten initialisieren
        console.log('\n📦 Initialisiere Komponenten...');
        
        dbManager = new DatabaseManager();
        await dbManager.connect();
        console.log('  ✅ Datenbank verbunden');

        dataProcessor = new DataProcessor(dbManager);
        console.log('  ✅ Data Processor bereit');
        
        // Daten aus dem vorherigen Scraping-Lauf rekonstruieren
        const scrapedResults = [
            // Langbahn Ergebnisse
            // Baden - Langbahn
            { rank: 1, swimmerName: 'Benedikt Paul', time: '0:35,71', club: 'Gundelfinger TS 1976', region: 'Baden', course: 'Langbahn (50m)', courseCode: 'L' },
            { rank: 2, swimmerName: 'Ats Alfred Pruunsild', time: '0:38,15', club: 'SV Nikar Heidelberg', region: 'Baden', course: 'Langbahn (50m)', courseCode: 'L' },
            { rank: 3, swimmerName: 'Kai Duong', time: '0:38,35', club: 'SG Region Karlsruhe', region: 'Baden', course: 'Langbahn (50m)', courseCode: 'L' },
            
            // Bayern - Langbahn
            { rank: 1, swimmerName: 'Lukas Wällisch', time: '0:37,15', club: 'SSG 81 Erlangen', region: 'Bayern', course: 'Langbahn (50m)', courseCode: 'L' },
            { rank: 2, swimmerName: 'Lorenz Warnke', time: '0:37,83', club: 'SV Fürstenfeldbrucker Wasserratten', region: 'Bayern', course: 'Langbahn (50m)', courseCode: 'L' },
            { rank: 3, swimmerName: 'Jakob Bschirrer', time: '0:37,85', club: '1.FCN Schwimmen', region: 'Bayern', course: 'Langbahn (50m)', courseCode: 'L' },
            
            // Brandenburg - Langbahn
            { rank: 1, swimmerName: 'Anton Weska', time: '0:37,05', club: 'SG Einheit Rathenow', region: 'Brandenburg', course: 'Langbahn (50m)', courseCode: 'L' },
            { rank: 2, swimmerName: 'Bent Wägner', time: '0:37,84', club: 'SC Potsdam', region: 'Brandenburg', course: 'Langbahn (50m)', courseCode: 'L' },
            { rank: 3, swimmerName: 'Pepe Tiersch', time: '0:38,62', club: 'SG Einheit Rathenow', region: 'Brandenburg', course: 'Langbahn (50m)', courseCode: 'L' },
            
            // Hamburg - Langbahn
            { rank: 1, swimmerName: 'Benjamin Danger', time: '0:43,73', club: 'Hamburger SC r.V. von 1879', region: 'Hamburg', course: 'Langbahn (50m)', courseCode: 'L' },
            { rank: 2, swimmerName: 'Niklas Leon Völker', time: '0:44,87', club: 'AWV 09 Hamburg', region: 'Hamburg', course: 'Langbahn (50m)', courseCode: 'L' },
            { rank: 3, swimmerName: 'Mark Kutschkanow', time: '0:46,11', club: 'HNT Hamburg', region: 'Hamburg', course: 'Langbahn (50m)', courseCode: 'L' },
            
            // Württemberg - Langbahn
            { rank: 1, swimmerName: 'Michael Pruschinskiy', time: '0:35,91', club: 'SV Region Stuttgart', region: 'Württemberg', course: 'Langbahn (50m)', courseCode: 'L' },
            { rank: 2, swimmerName: 'Aurel Marquardt', time: '0:38,95', club: 'SV Waiblingen', region: 'Württemberg', course: 'Langbahn (50m)', courseCode: 'L' },
            { rank: 3, swimmerName: 'Robert Indlekofer', time: '0:39,52', club: 'SV Cannstatt', region: 'Württemberg', course: 'Langbahn (50m)', courseCode: 'L' },
            
            // Kurzbahn Ergebnisse
            // Baden - Kurzbahn
            { rank: 1, swimmerName: 'Kai Duong', time: '0:39,58', club: 'SG Region Karlsruhe', region: 'Baden', course: 'Kurzbahn (25m)', courseCode: 'S' },
            { rank: 2, swimmerName: 'Benedikt Paul', time: '0:39,67', club: 'Gundelfinger TS 1976', region: 'Baden', course: 'Kurzbahn (25m)', courseCode: 'S' },
            { rank: 3, swimmerName: 'Luka Djordjevic', time: '0:41,03', club: 'SC Villingen 1950', region: 'Baden', course: 'Kurzbahn (25m)', courseCode: 'S' },
            
            // Bayern - Kurzbahn
            { rank: 1, swimmerName: 'Timo Deng', time: '0:37,24', club: 'SSG Neptun Germering', region: 'Bayern', course: 'Kurzbahn (25m)', courseCode: 'S' },
            { rank: 2, swimmerName: 'Lian Bravo Rodriguez', time: '0:40,13', club: 'TSV Obergünzburg', region: 'Bayern', course: 'Kurzbahn (25m)', courseCode: 'S' },
            { rank: 3, swimmerName: 'Lorenz Warnke', time: '0:40,44', club: 'SV Fürstenfeldbrucker Wasserratten', region: 'Bayern', course: 'Kurzbahn (25m)', courseCode: 'S' },
            
            // Brandenburg - Kurzbahn
            { rank: 1, swimmerName: 'Anton Weska', time: '0:35,96', club: 'SG Einheit Rathenow', region: 'Brandenburg', course: 'Kurzbahn (25m)', courseCode: 'S' },
            { rank: 2, swimmerName: 'Pepe Tiersch', time: '0:36,42', club: 'SG Einheit Rathenow', region: 'Brandenburg', course: 'Kurzbahn (25m)', courseCode: 'S' },
            { rank: 3, swimmerName: 'Maximilian Kretzschmar', time: '0:41,92', club: 'SG Einheit Rathenow', region: 'Brandenburg', course: 'Kurzbahn (25m)', courseCode: 'S' },
            
            // Hamburg - Kurzbahn
            { rank: 1, swimmerName: 'Jakob Piet Petrenz', time: '0:50,27', club: 'SG Hamburg-West', region: 'Hamburg', course: 'Kurzbahn (25m)', courseCode: 'S' },
            { rank: 2, swimmerName: 'Maxim Ovcharenko', time: '0:51,94', club: 'SV Poseidon Hamburg', region: 'Hamburg', course: 'Kurzbahn (25m)', courseCode: 'S' },
            { rank: 3, swimmerName: 'Flynn Benson Gong', time: '0:52,80', club: 'SG HT16 Hamburg', region: 'Hamburg', course: 'Kurzbahn (25m)', courseCode: 'S' },
            
            // Saarland - Kurzbahn
            { rank: 1, swimmerName: 'Henry Keßler', time: '0:40,96', club: 'SSG Saar Max Ritter', region: 'Saarland', course: 'Kurzbahn (25m)', courseCode: 'S' },
            { rank: 2, swimmerName: 'Paul Markow', time: '0:41,03', club: 'SSG Saar Max Ritter', region: 'Saarland', course: 'Kurzbahn (25m)', courseCode: 'S' },
            { rank: 3, swimmerName: 'Raphael Heider', time: '0:45,41', club: 'SSG Saar Max Ritter', region: 'Saarland', course: 'Kurzbahn (25m)', courseCode: 'S' },
            
            // Sachsen-Anhalt - Kurzbahn
            { rank: 1, swimmerName: 'Jakob Weber', time: '0:39,16', club: 'SSV 70 Halle-Neustadt', region: 'Sachsen-Anhalt', course: 'Kurzbahn (25m)', courseCode: 'S' },
            { rank: 2, swimmerName: 'Dwayne Kristof Kunth', time: '0:45,17', club: 'HSV Wernigerode 2002', region: 'Sachsen-Anhalt', course: 'Kurzbahn (25m)', courseCode: 'S' },
            { rank: 3, swimmerName: 'Julian Richter', time: '0:45,23', club: 'SSV 70 Halle-Neustadt', region: 'Sachsen-Anhalt', course: 'Kurzbahn (25m)', courseCode: 'S' },
            
            // Südwest - Kurzbahn
            { rank: 1, swimmerName: 'Jay Kamol Kriengsakpichit', time: '0:37,73', club: 'Frankenthaler SV 1897', region: 'Südwest', course: 'Kurzbahn (25m)', courseCode: 'S' },
            { rank: 2, swimmerName: 'Raylan Benton', time: '0:39,53', club: 'Aqua-Kids Kaiserslautern', region: 'Südwest', course: 'Kurzbahn (25m)', courseCode: 'S' },
            { rank: 3, swimmerName: 'Robert J Mallory', time: '0:42,14', club: 'Aqua-Kids Kaiserslautern', region: 'Südwest', course: 'Kurzbahn (25m)', courseCode: 'S' },
            
            // Württemberg - Kurzbahn
            { rank: 1, swimmerName: 'Tim Boese', time: '0:41,51', club: 'VfL Sindelfingen', region: 'Württemberg', course: 'Kurzbahn (25m)', courseCode: 'S' },
            { rank: 2, swimmerName: 'Jonah Horn', time: '0:43,23', club: 'SV Region Stuttgart', region: 'Württemberg', course: 'Kurzbahn (25m)', courseCode: 'S' },
            { rank: 3, swimmerName: 'Benjamin Bauer', time: '0:43,31', club: 'VfL Sindelfingen', region: 'Württemberg', course: 'Kurzbahn (25m)', courseCode: 'S' }
        ];
        
        // Event-Informationen hinzufügen
        const processedResults = scrapedResults.map(result => ({
            ...result,
            eventName: '50m Schmetterling',
            eventCode: '50S|GL',
            distance: 50,
            stroke: 'Schmetterling',
            gender: 'M',
            ageGroup: '2015|2015',
            season: '2025',
            timeSeconds: timeToSeconds(result.time),
            birthYear: 2015, // Jahrgang 2015
            scrapedAt: new Date()
        }));
        
        console.log(`\n📊 Vorbereitet: ${processedResults.length} Datensätze`);
        console.log('Event: 50m Schmetterling, Jahrgang 2015, männlich');
        
        // Daten in Datenbank speichern
        console.log('\n💾 Speichere in Datenbank...');

        // Daten nach Event-Key gruppieren
        const eventKey = '50S|GL_M_2015|2015_2025';
        await dataProcessor.processEventResults(eventKey, processedResults);
        
        console.log(`✅ ${processedResults.length} Datensätze erfolgreich gespeichert!`);
        
        // Statistiken anzeigen
        const regions = [...new Set(processedResults.map(r => r.region))];
        const courses = [...new Set(processedResults.map(r => r.course))];
        
        console.log('\n📈 Gespeicherte Daten:');
        console.log(`  Regionen: ${regions.length} (${regions.join(', ')})`);
        console.log(`  Kurse: ${courses.length} (${courses.join(', ')})`);
        console.log(`  Schwimmer: ${[...new Set(processedResults.map(r => r.swimmerName))].length} eindeutige`);
        
        // Beste Zeiten
        const langbahnResults = processedResults.filter(r => r.courseCode === 'L');
        const kurbbahnResults = processedResults.filter(r => r.courseCode === 'S');
        
        if (langbahnResults.length > 0) {
            const bestLangbahn = langbahnResults.sort((a, b) => a.time.localeCompare(b.time))[0];
            console.log(`\n🥇 Beste Zeit Langbahn: ${bestLangbahn.swimmerName} - ${bestLangbahn.time} (${bestLangbahn.club}, ${bestLangbahn.region})`);
        }
        
        if (kurbbahnResults.length > 0) {
            const bestKurzbahn = kurbbahnResults.sort((a, b) => a.time.localeCompare(b.time))[0];
            console.log(`🥇 Beste Zeit Kurzbahn: ${bestKurzbahn.swimmerName} - ${bestKurzbahn.time} (${bestKurzbahn.club}, ${bestKurzbahn.region})`);
        }
        
        console.log('\n🎉 Daten erfolgreich in Datenbank gespeichert!');
        
    } catch (error) {
        console.error('\n❌ Fehler beim Speichern:', error.message);
        console.error('Stack:', error.stack);
    } finally {
        // Cleanup
        if (dbManager) {
            await dbManager.disconnect();
            console.log('📦 Datenbankverbindung geschlossen');
        }
    }
}

// Speichern starten
savePreviousResults().catch(console.error);

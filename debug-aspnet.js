#!/usr/bin/env node

/**
 * Debug Script für ASP.NET Form-Felder
 * Analysiert alle Form-Felder auf der DSV-Seite
 */

import axios from 'axios';
import * as cheerio from 'cheerio';

async function debugAspNet() {
    console.log('🔍 Debug ASP.NET Form-Felder');
    console.log('=============================');
    
    try {
        const url = 'https://dsvdaten.dsv.de/Modules/Clubs/Index.aspx?StateID=1';
        
        console.log(`\n📡 GET Request: ${url}`);
        
        const response = await axios.get(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'de-DE,de;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive'
            },
            timeout: 30000
        });
        
        console.log(`✅ Status: ${response.status}`);
        
        const $ = cheerio.load(response.data);
        
        // Alle Input-Felder analysieren
        console.log('\n🔍 Alle Input-Felder:');
        const inputs = $('input');
        console.log(`Gefunden: ${inputs.length} Input-Felder`);
        
        inputs.each((i, el) => {
            const $el = $(el);
            const type = $el.attr('type') || 'text';
            const name = $el.attr('name') || 'UNNAMED';
            const value = $el.attr('value') || $el.val() || '';
            const id = $el.attr('id') || '';
            
            if (name.includes('ContentSection') || type === 'hidden' || name.includes('__')) {
                console.log(`  ${i+1}. [${type}] ${name}`);
                if (id) console.log(`      id: ${id}`);
                if (value && value.length < 100) {
                    console.log(`      value: ${value}`);
                } else if (value) {
                    console.log(`      value: ${value.substring(0, 50)}... (${value.length} chars)`);
                }
            }
        });
        
        // Alle Select-Felder analysieren
        console.log('\n📋 Alle Select-Felder:');
        const selects = $('select');
        console.log(`Gefunden: ${selects.length} Select-Felder`);
        
        selects.each((i, el) => {
            const $el = $(el);
            const name = $el.attr('name') || 'UNNAMED';
            const id = $el.attr('id') || '';
            const selectedValue = $el.find('option:selected').val() || '';
            const selectedText = $el.find('option:selected').text().trim() || '';
            
            console.log(`  ${i+1}. ${name}`);
            if (id) console.log(`      id: ${id}`);
            if (selectedValue) console.log(`      selected: ${selectedValue} (${selectedText})`);
            
            // Optionen anzeigen
            const options = $el.find('option');
            if (options.length <= 10) {
                options.each((j, opt) => {
                    const $opt = $(opt);
                    const val = $opt.val() || '';
                    const text = $opt.text().trim() || '';
                    if (val) {
                        console.log(`        - ${val}: ${text}`);
                    }
                });
            } else {
                console.log(`        (${options.length} Optionen verfügbar)`);
            }
        });
        
        // Radio Buttons analysieren
        console.log('\n📻 Radio Button Groups:');
        const radioGroups = {};
        $('input[type="radio"]').each((i, el) => {
            const $el = $(el);
            const name = $el.attr('name') || 'UNNAMED';
            const value = $el.attr('value') || '';
            const checked = $el.prop('checked');
            const id = $el.attr('id') || '';
            
            if (!radioGroups[name]) {
                radioGroups[name] = [];
            }
            radioGroups[name].push({ value, checked, id });
        });
        
        Object.keys(radioGroups).forEach(groupName => {
            console.log(`  ${groupName}:`);
            radioGroups[groupName].forEach(radio => {
                const status = radio.checked ? '(selected)' : '';
                console.log(`    - ${radio.value} ${status}`);
                if (radio.id) console.log(`      id: ${radio.id}`);
            });
        });
        
        // Versteckte ASP.NET Felder
        console.log('\n🔐 Versteckte ASP.NET Felder:');
        const hiddenFields = {};
        $('input[type="hidden"]').each((i, el) => {
            const $el = $(el);
            const name = $el.attr('name');
            const value = $el.val() || '';
            
            if (name && (name.startsWith('__') || name.includes('ViewState') || name.includes('EventValidation'))) {
                hiddenFields[name] = value;
                console.log(`  ${name}: ${value.length} chars`);
                if (value.length < 50) {
                    console.log(`    value: ${value}`);
                }
            }
        });
        
        // Test: Einfacher POST mit allen Feldern
        console.log('\n📤 Test: POST mit Event-Änderung');
        
        // Alle Form-Felder sammeln
        const allFormData = {};
        
        // Versteckte Felder
        Object.assign(allFormData, hiddenFields);
        
        // Alle anderen Input-Felder
        $('input:not([type="hidden"]):not([type="radio"]):not([type="checkbox"])').each((i, el) => {
            const $el = $(el);
            const name = $el.attr('name');
            const value = $el.val() || $el.attr('value') || '';
            if (name) {
                allFormData[name] = value;
            }
        });
        
        // Select-Felder
        $('select').each((i, el) => {
            const $el = $(el);
            const name = $el.attr('name');
            const value = $el.find('option:selected').val() || $el.find('option').first().val() || '';
            if (name) {
                allFormData[name] = value;
            }
        });
        
        // Radio Buttons (nur die checked ones)
        $('input[type="radio"]:checked').each((i, el) => {
            const $el = $(el);
            const name = $el.attr('name');
            const value = $el.val() || '';
            if (name) {
                allFormData[name] = value;
            }
        });
        
        // Überschreibe mit unseren gewünschten Werten
        allFormData['ctl00$ContentSection$_eventDropDownList'] = '100F|GL'; // 100m Freistil
        allFormData['ctl00$ContentSection$_genderRadioButtonList'] = 'W'; // Weiblich
        allFormData['ctl00$ContentSection$_courseRadioButtonList'] = 'S'; // Kurzbahn
        
        console.log(`Sende ${Object.keys(allFormData).length} Form-Felder`);
        console.log('Wichtige Felder:');
        console.log(`  Event: ${allFormData['ctl00$ContentSection$_eventDropDownList']}`);
        console.log(`  Gender: ${allFormData['ctl00$ContentSection$_genderRadioButtonList']}`);
        console.log(`  Course: ${allFormData['ctl00$ContentSection$_courseRadioButtonList']}`);
        console.log(`  Region: ${allFormData['ctl00$ContentSection$_regionsDropDownList']}`);
        
        const postData = new URLSearchParams(allFormData).toString();
        
        const postResponse = await axios.post(url, postData, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'de-DE,de;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Referer': url,
                'Origin': 'https://dsvdaten.dsv.de'
            },
            timeout: 30000
        });
        
        console.log(`✅ POST Status: ${postResponse.status}`);
        
        const $post = cheerio.load(postResponse.data);
        
        // Prüfe ob sich die Dropdowns geändert haben
        const newEventValue = $post('#ContentSection__eventDropDownList').val();
        const newGenderValue = $post('input[name="ctl00$ContentSection$_genderRadioButtonList"]:checked').val();
        const newCourseValue = $post('input[name="ctl00$ContentSection$_courseRadioButtonList"]:checked').val();
        
        console.log('\n🔍 Nach POST:');
        console.log(`  Event Dropdown: ${newEventValue}`);
        console.log(`  Gender Radio: ${newGenderValue}`);
        console.log(`  Course Radio: ${newCourseValue}`);
        
        // Prüfe ob Rankings-Tabelle vorhanden ist
        const rankingsTable = $post('#ContentSection__rankingsGridView');
        const rankingsRows = $post('#ContentSection__rankingsGridView tr');
        
        console.log(`\n📊 Rankings:`);
        console.log(`  Tabelle gefunden: ${rankingsTable.length > 0}`);
        console.log(`  Zeilen: ${rankingsRows.length}`);
        
        if (rankingsRows.length > 1) {
            console.log('\n🎉 SUCCESS: Rankings gefunden!');
            rankingsRows.slice(1, 4).each((i, row) => {
                const cells = $post(row).find('td');
                if (cells.length > 0) {
                    const rank = $post(cells[0]).text().trim();
                    const name = $post(cells[1]).text().trim();
                    const time = $post(cells[2]).text().trim();
                    console.log(`   ${rank}. ${name} - ${time}`);
                }
            });
        } else {
            console.log('\n⚠️  Keine Rankings gefunden');
        }
        
    } catch (error) {
        console.error('\n❌ Fehler beim Debug:', error.message);
        if (error.response) {
            console.error(`   Status: ${error.response.status}`);
        }
        console.error(`   Stack:`, error.stack);
    }
}

// Führe Debug aus
debugAspNet().catch(console.error);

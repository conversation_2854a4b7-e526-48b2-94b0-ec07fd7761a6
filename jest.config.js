/**
 * Jest Configuration für DSV Scraper - Node.js Version
 */
export default {
  // Test Environment
  testEnvironment: 'node',
  
  // Test Files
  testMatch: [
    '**/tests/**/*.test.js',
    '**/tests/**/*.spec.js'
  ],
  
  // Coverage
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/**/*.test.js',
    '!src/**/*.spec.js',
    '!src/web/public/**',
    '!src/web/views/**'
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },
  
  // Setup Files
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  
  // Test Timeout
  testTimeout: 30000,
  
  // Verbose Output
  verbose: true,
  
  // Clear Mocks
  clearMocks: true,
  restoreMocks: true,
  
  // Transform - disable for ES modules
  transform: {}
};

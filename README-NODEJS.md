# DSV Rankings - Node.js Version

Ein umfassendes System für das automatisierte Scraping und die Darstellung von DSV-Schwimmdaten, komplett in Node.js umgeschrieben.

## 🏊‍♂️ Features

- **Automatisiertes Scraping**: Wöchentliches Scraping von DSV-Daten mit Puppeteer
- **Express Web-Dashboard**: Moderne, responsive Web-Oberfläche
- **Prisma Database**: Type-safe Datenbankzugriff mit PostgreSQL/SQLite
- **Appwrite Integration**: Cloud-Deployment mit Appwrite Functions
- **Cron Scheduling**: Automatisierte Jobs mit node-cron
- **Comprehensive Logging**: Winston-basiertes Logging-System
- **Email & Slack Notifications**: Benachrichtigungen bei Erfolg/Fehlern
- **CSV Export**: Datenexport für weitere Analysen
- **Docker Support**: Containerisierte Deployment-Lösung

## 🚀 Schnellstart

### Voraussetzungen

- Node.js 18.0.0+
- npm 9.0.0+
- PostgreSQL (optional, SQLite als Alternative)
- Docker & Docker Compose (für Container-Deployment)

### Lokale Installation

1. **Repository klonen**
```bash
git clone <repository-url>
cd dsv-scraper
```

2. **Dependencies installieren**
```bash
npm install
```

3. **Environment-Datei erstellen**
```bash
cp .env.nodejs.example .env
# Bearbeite .env mit deinen Konfigurationen
```

4. **Datenbank initialisieren**
```bash
npm run db:generate
npm run db:push
```

5. **Server starten**
```bash
# Development
npm run dev

# Production
npm start
```

6. **Scheduler starten (optional)**
```bash
npm run scheduler
```

## 📁 Projektstruktur

```
dsv-scraper/
├── src/
│   ├── config.js              # Zentrale Konfiguration
│   ├── index.js               # Web-App Entry Point
│   ├── scheduler.js           # Scheduler Entry Point
│   ├── web/                   # Express Web Application
│   │   ├── app.js
│   │   ├── routes/
│   │   │   ├── api.js         # API Routes
│   │   │   └── web.js         # Web Routes
│   │   ├── views/             # EJS Templates
│   │   └── public/            # Statische Dateien
│   ├── scrapers/              # Scraping-Module
│   │   ├── BaseScraper.js
│   │   ├── DSVScraper.js
│   │   └── config/
│   │       └── events.json
│   ├── utils/                 # Hilfsfunktionen
│   │   ├── DatabaseManager.js
│   │   ├── AppwriteDatabase.js
│   │   ├── DataProcessor.js
│   │   └── logger.js
│   └── scheduler/             # Scheduling & Notifications
│       ├── CronJobs.js
│       └── Notifications.js
├── prisma/
│   └── schema.prisma          # Datenbankschema
├── data/                      # Datenverzeichnisse
├── logs/                      # Log-Dateien
├── tests/                     # Tests
├── package.json
└── README-NODEJS.md
```

## 🗄️ Datenbank

### Prisma Setup

```bash
# Schema generieren
npm run db:generate

# Datenbank migrieren
npm run db:migrate

# Datenbank Studio öffnen
npm run db:studio
```

### Unterstützte Datenbanken

- **PostgreSQL** (empfohlen für Production)
- **SQLite** (für Development)
- **Appwrite Database** (für Cloud-Deployment)

## 🌐 API Endpoints

### Web Interface
- `GET /` - Dashboard
- `GET /rankings` - Rankings-Übersicht
- `GET /events` - Events-Verwaltung
- `GET /regions` - Regionen-Übersicht
- `GET /logs` - Scraping-Logs
- `GET /statistics` - Statistiken

### REST API
- `GET /api/rankings` - Rankings abrufen
- `GET /api/rankings/top` - Top Rankings
- `GET /api/events` - Events abrufen
- `GET /api/regions` - Regionen abrufen
- `GET /api/statistics` - Statistiken
- `GET /api/scrape-logs` - Scraping-Logs
- `GET /api/export/csv` - CSV Export

### Health Check
- `GET /health` - System-Gesundheitsprüfung

## ⏰ Scheduler

### Automatische Jobs

- **Wöchentliches Scraping**: Sonntags 06:00 UTC
- **Tägliche Bereinigung**: Täglich 02:00 UTC
- **Stündliche Gesundheitsprüfung**: Jede volle Stunde

### Manuelle Ausführung

```bash
# Scheduler starten
npm run scheduler

# Einzelne Jobs (via API)
curl "http://localhost:3000/scheduler?action=weekly_scrape"
curl "http://localhost:3000/scheduler?action=health_check"
curl "http://localhost:3000/scheduler?action=cleanup"
```

## 🔧 Konfiguration

### Environment-Variablen

```bash
# Node.js
NODE_ENV=development
PORT=3000

# Database
DATABASE_TYPE=postgresql
DATABASE_URL=postgresql://user:pass@localhost:5432/dsv_rankings

# Scraping
SCRAPING_DELAY=1
MAX_RETRIES=3
USER_AGENT=Mozilla/5.0...

# Scheduling
ENABLE_SCHEDULER=true
SCRAPE_TIME=06:00
SCRAPE_DAY=sunday

# Notifications
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password
SLACK_WEBHOOK_URL=your-slack-webhook
```

### Events-Konfiguration

Bearbeite `src/scrapers/config/events.json` um Events zu aktivieren/deaktivieren:

```json
{
  "events": [
    {
      "name": "200m Lagen",
      "code": "200L|GL",
      "enabled": true,
      "priority": 1
    }
  ]
}
```

## 🚀 Deployment

### Appwrite Cloud

1. **Appwrite-Konfiguration**
```bash
cp .env.appwrite.nodejs.example .env.appwrite
# Bearbeite .env.appwrite mit Appwrite-Credentials
```

2. **Deployment**
```bash
npm run deploy:appwrite
```

### Docker

```bash
# Build
docker build -t dsv-scraper-nodejs .

# Run
docker run -p 3000:3000 dsv-scraper-nodejs
```

### Docker Compose

```bash
docker-compose up -d
```

## 🧪 Testing

```bash
# Alle Tests
npm test

# Tests mit Watch-Mode
npm run test:watch

# Linting
npm run lint

# Code-Formatierung
npm run format
```

## 📊 Monitoring & Logging

### Logs

- **Console**: Development-Logs mit Farben
- **Files**: Production-Logs in `logs/` Verzeichnis
- **Rotation**: Tägliche Log-Rotation mit Aufbewahrung

### Benachrichtigungen

- **Email**: SMTP-basierte Email-Benachrichtigungen
- **Slack**: Webhook-basierte Slack-Nachrichten
- **Kategorien**: Success, Error, Warning, Health Alerts

## 🔄 Migration von Python

### Hauptunterschiede

| Python Version | Node.js Version |
|----------------|-----------------|
| Flask | Express.js |
| SQLAlchemy | Prisma |
| BeautifulSoup + Requests | Cheerio + Axios |
| APScheduler | node-cron |
| pytest | Jest |

### Daten-Migration

Die Datenbankstruktur ist kompatibel. Bestehende PostgreSQL-Daten können weiterverwendet werden.

## 🤝 Contributing

1. Fork das Repository
2. Erstelle einen Feature-Branch
3. Committe deine Änderungen
4. Push zum Branch
5. Erstelle einen Pull Request

## 📝 License

MIT License - siehe LICENSE-Datei für Details.

## 🆘 Support

- **Issues**: GitHub Issues für Bug-Reports
- **Discussions**: GitHub Discussions für Fragen
- **Email**: [Deine Email] für direkten Support

---

**Hinweis**: Dies ist die Node.js-Version des DSV Scrapers. Die ursprüngliche Python-Version ist weiterhin verfügbar.

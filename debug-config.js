#!/usr/bin/env node

/**
 * Debug Script für Scraping-Konfiguration
 * Testet, welche Parameter wirklich an scrapeEvent übergeben werden
 */

import { DSVScraper } from './src/scrapers/DSVScraper.js';

async function debugConfig() {
    console.log('🔍 Debug Scraping-Konfiguration');
    console.log('================================');
    
    try {
        // DSV Scraper initialisieren
        const scraper = new DSVScraper('scrapers/config/events.json');

        // Konfiguration laden
        await scraper.loadConfig();

        console.log('\n📋 Geladene Konfiguration:');
        console.log('Events:', scraper.config.events.length);
        console.log('Genders:', scraper.config.genders.length);
        console.log('Courses:', scraper.config.courses.length);
        console.log('Regions:', scraper.config.regions.length);
        
        // <PERSON>rste paar Events anzeigen
        console.log('\n📋 Erste 3 Events:');
        scraper.config.events.slice(0, 3).forEach((event, i) => {
            console.log(`  ${i+1}. ${event.name} (${event.code}) - enabled: ${event.enabled}`);
        });
        
        // Genders anzeigen
        console.log('\n👫 Genders:');
        scraper.config.genders.forEach((gender, i) => {
            console.log(`  ${i+1}. ${gender.name} (${gender.code}) - enabled: ${gender.enabled}`);
        });
        
        // Courses anzeigen
        console.log('\n🏊 Courses:');
        scraper.config.courses.forEach((course, i) => {
            console.log(`  ${i+1}. ${course.name} (${course.code}) - enabled: ${course.enabled}`);
        });
        
        // Simuliere die Schleifenlogik aus BaseScraper
        console.log('\n🔄 Simuliere Schleifenlogik:');
        
        const events = scraper.config.events.filter(e => e.enabled);
        const genders = scraper.config.genders.filter(g => g.enabled).map(g => g.code);
        const courses = scraper.config.courses.filter(c => c.enabled).map(c => c.code);
        const regions = scraper.config.regions.slice(0, 2); // Nur erste 2 Regionen für Test
        const season = scraper.config.seasons.find(s => s.enabled)?.year || '2025';
        
        console.log(`Gefilterte Events: ${events.length}`);
        console.log(`Gefilterte Genders: ${genders.length} (${genders.join(', ')})`);
        console.log(`Gefilterte Courses: ${courses.length} (${courses.join(', ')})`);
        console.log(`Test-Regionen: ${regions.length}`);
        
        let combinationCount = 0;
        
        for (const event of events.slice(0, 2)) { // Nur erste 2 Events für Test
            for (const gender of genders) {
                for (const course of courses) {
                    for (const region of regions) {
                        combinationCount++;
                        
                        const scrapingConfig = {
                            eventName: event.name,
                            eventCode: event.code,
                            distance: event.distance,
                            stroke: event.stroke,
                            course: course,
                            gender,
                            season,
                            regionId: region.id
                        };
                        
                        console.log(`\n${combinationCount}. Kombination:`);
                        console.log(`   Event: ${scrapingConfig.eventName} (${scrapingConfig.eventCode})`);
                        console.log(`   Gender: ${scrapingConfig.gender}`);
                        console.log(`   Course: ${scrapingConfig.course}`);
                        console.log(`   Region: ${scrapingConfig.regionId} (${region.name})`);
                        
                        // Simuliere _buildCompletePayload
                        const payload = {
                            'ctl00$ContentSection$_regionsDropDownList': scrapingConfig.regionId?.toString() || '1',
                            'ctl00$ContentSection$_seasonDropDownList': scrapingConfig.season || '2025',
                            'ctl00$ContentSection$_eventDropDownList': scrapingConfig.eventCode || '50F|GL',
                            'ctl00$ContentSection$_genderRadioButtonList': scrapingConfig.gender || 'M',
                            'ctl00$ContentSection$_courseRadioButtonList': scrapingConfig.course === 'L' ? 'L' : 'S'
                        };
                        
                        console.log(`   Payload:`);
                        console.log(`     Event: ${payload['ctl00$ContentSection$_eventDropDownList']}`);
                        console.log(`     Gender: ${payload['ctl00$ContentSection$_genderRadioButtonList']}`);
                        console.log(`     Course: ${payload['ctl00$ContentSection$_courseRadioButtonList']}`);
                        console.log(`     Region: ${payload['ctl00$ContentSection$_regionsDropDownList']}`);
                        
                        if (combinationCount >= 8) { // Stoppe nach 8 Kombinationen
                            console.log('\n... (weitere Kombinationen abgeschnitten)');
                            break;
                        }
                    }
                    if (combinationCount >= 8) break;
                }
                if (combinationCount >= 8) break;
            }
            if (combinationCount >= 8) break;
        }
        
        console.log(`\n✅ Insgesamt ${combinationCount} Kombinationen getestet`);
        
    } catch (error) {
        console.error('\n❌ Fehler beim Debug:', error.message);
        console.error(`   Stack:`, error.stack);
    }
}

// Führe Debug aus
debugConfig().catch(console.error);

#!/usr/bin/env node

/**
 * Debug Script für Altersklassen
 * Analysiert das ContentSection__ageDropDownList Feld
 */

import axios from 'axios';
import * as cheerio from 'cheerio';

async function debugAgeGroups() {
    console.log('🔍 Debug: Altersklassen (Age Groups)');
    console.log('====================================');
    
    try {
        const url = 'https://dsvdaten.dsv.de/Modules/Clubs/Index.aspx?StateID=1';
        
        console.log(`\n📡 GET Request: ${url}`);
        
        const response = await axios.get(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'de-DE,de;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive'
            },
            timeout: 30000
        });
        
        console.log(`✅ Status: ${response.status}`);
        
        const $ = cheerio.load(response.data);
        
        // Age Dropdown analysieren
        console.log('\n👶 Age Dropdown Analyse:');
        const ageDropdown = $('#ContentSection__ageDropDownList');
        console.log(`Dropdown gefunden: ${ageDropdown.length > 0}`);
        
        if (ageDropdown.length > 0) {
            const ageOptions = ageDropdown.find('option');
            console.log(`Anzahl Optionen: ${ageOptions.length}`);
            
            console.log('\n📋 Alle Altersklassen:');
            ageOptions.each((i, el) => {
                const $el = $(el);
                const value = $el.val() || '';
                const text = $el.text().trim() || '';
                const selected = $el.prop('selected');
                
                if (value || text) {
                    console.log(`  ${i+1}. Value: "${value}" | Text: "${text}" ${selected ? '(selected)' : ''}`);
                }
            });
            
            // Spezifisch nach 2015 suchen
            console.log('\n🔍 Suche nach Jahrgang 2015:');
            let found2015 = false;
            ageOptions.each((i, el) => {
                const $el = $(el);
                const value = $el.val() || '';
                const text = $el.text().trim() || '';
                
                if (value.includes('2015') || text.includes('2015')) {
                    console.log(`  ✅ GEFUNDEN: Value: "${value}" | Text: "${text}"`);
                    found2015 = true;
                }
            });
            
            if (!found2015) {
                console.log('  ⚠️  Jahrgang 2015 nicht direkt gefunden');
                console.log('  💡 Mögliche Alternativen:');
                ageOptions.each((i, el) => {
                    const $el = $(el);
                    const value = $el.val() || '';
                    const text = $el.text().trim() || '';
                    
                    // Suche nach ähnlichen Jahrgängen
                    if (value.includes('201') || text.includes('201') || 
                        value.includes('2014') || text.includes('2014') ||
                        value.includes('2016') || text.includes('2016')) {
                        console.log(`    - Value: "${value}" | Text: "${text}"`);
                    }
                });
            }
        } else {
            console.log('❌ Age Dropdown nicht gefunden!');
            
            // Alternative Suche
            console.log('\n🔍 Alternative Suche nach Age-Feldern:');
            $('select').each((i, el) => {
                const $el = $(el);
                const id = $el.attr('id') || '';
                const name = $el.attr('name') || '';
                
                if (id.toLowerCase().includes('age') || name.toLowerCase().includes('age') ||
                    id.toLowerCase().includes('alter') || name.toLowerCase().includes('alter')) {
                    console.log(`  Gefunden: id="${id}" name="${name}"`);
                    
                    const options = $el.find('option');
                    console.log(`    ${options.length} Optionen:`);
                    options.slice(0, 5).each((j, opt) => {
                        const $opt = $(opt);
                        const value = $opt.val() || '';
                        const text = $opt.text().trim() || '';
                        console.log(`      - "${value}": "${text}"`);
                    });
                    if (options.length > 5) {
                        console.log(`      ... und ${options.length - 5} weitere`);
                    }
                }
            });
        }
        
        // Test: POST mit Altersklasse
        console.log('\n📤 Test: POST mit Altersklasse');
        
        // Versteckte Felder extrahieren
        const hiddenFields = {};
        $('input[type="hidden"]').each((i, el) => {
            const $el = $(el);
            const name = $el.attr('name');
            const value = $el.val() || '';
            if (name && (name.startsWith('__') || name.includes('ViewState') || name.includes('EventValidation'))) {
                hiddenFields[name] = value;
            }
        });
        
        // Teste mit einem verfügbaren Altersklassen-Wert
        const firstAgeOption = $('#ContentSection__ageDropDownList option').eq(1); // Erste echte Option (nicht "Alle")
        const ageValue = firstAgeOption.val() || '';
        const ageText = firstAgeOption.text().trim() || '';
        
        if (ageValue) {
            console.log(`Teste mit Altersklasse: "${ageValue}" (${ageText})`);
            
            const postData = new URLSearchParams({
                ...hiddenFields,
                'ctl00$ContentSection$_regionsDropDownList': '1',
                'ctl00$ContentSection$_seasonDropDownList': '2025',
                'ctl00$ContentSection$_eventDropDownList': '50S|GL',
                'ctl00$ContentSection$_ageDropDownList': ageValue,
                'ctl00$ContentSection$_genderRadioButtonList': 'M',
                'ctl00$ContentSection$_courseRadioButtonList': 'L',
                'ctl00$ContentSection$_timerangeDropDownList': '01.01.2025|31.12.2025',
                'ctl00$ContentSection$_pointsDropDownList': 'FINA|2024|S',
                'ctl00$ContentSection$_pdfsDropDownList': '01.06.2024|31.05.2025',
                'ctl00$ContentSection$_r_agegroupDropdownlist': '10',
                'ctl00$ContentSection$_kader_seasonDropDownList': '',
                'ctl00$ContentSection$_r_genderRadiobuttonlist': 'M',
                'ctl00$ContentSection$_r_courseRadiobuttonlist': 'L',
                'ctl00$ContentSection$hiddenTab': '#meets',
                'ctl00$ContentSection$_rankingsButton': 'Daten laden'
            }).toString();
            
            const postResponse = await axios.post(url, postData, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'de-DE,de;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    'Referer': url,
                    'Origin': 'https://dsvdaten.dsv.de'
                },
                timeout: 30000
            });
            
            console.log(`✅ POST Status: ${postResponse.status}`);
            
            const $post = cheerio.load(postResponse.data);
            const rankingsTable = $post('#ContentSection__rankingsGridView');
            const rankingsRows = $post('#ContentSection__rankingsGridView tr');
            
            console.log(`📊 Rankings gefunden: ${rankingsRows.length > 1 ? rankingsRows.length - 1 : 0}`);
            
            if (rankingsRows.length > 1) {
                console.log('Erste 3 Ergebnisse:');
                rankingsRows.slice(1, 4).each((i, row) => {
                    const cells = $post(row).find('td');
                    if (cells.length > 0) {
                        const rank = $post(cells[0]).text().trim();
                        const name = $post(cells[1]).text().trim();
                        const time = $post(cells[2]).text().trim();
                        console.log(`  ${rank}. ${name} - ${time}`);
                    }
                });
            }
        } else {
            console.log('⚠️  Keine Altersklasse zum Testen gefunden');
        }
        
    } catch (error) {
        console.error('\n❌ Fehler beim Debug:', error.message);
        if (error.response) {
            console.error(`   Status: ${error.response.status}`);
        }
        console.error(`   Stack:`, error.stack);
    }
}

// Führe Debug aus
debugAgeGroups().catch(console.error);

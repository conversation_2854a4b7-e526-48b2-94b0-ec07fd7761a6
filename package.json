{"name": "dsv-scraper-nodejs", "version": "1.0.0", "description": "DSV Swimming Rankings Scraper - Node.js Version", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "scheduler": "node src/scheduler.js", "test": "node --experimental-vm-modules node_modules/.bin/jest", "test:watch": "node --experimental-vm-modules node_modules/.bin/jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "build": "echo 'No build step required for Node.js'", "scrape:initial": "node scripts/initial-scrape.js", "scrape:manual": "node scripts/manual-scrape.js"}, "prisma": {"seed": "node prisma/seed.js"}, "keywords": ["swimming", "scraping", "dsv", "rankings", "nodejs", "express", "appwrite"], "author": "DSV Scraper Team", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.1", "appwrite": "^13.0.1", "axios": "^1.6.2", "cheerio": "^1.0.0-rc.12", "compression": "^1.7.4", "config": "^3.3.9", "cors": "^2.8.5", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "dotenv": "^16.3.1", "ejs": "^3.1.9", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "lodash": "^4.17.21", "moment": "^2.29.4", "multer": "^2.0.2", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "pg": "^8.11.3", "prisma": "^5.7.1", "puppeteer": "^24.14.0", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "@types/jest": "^29.5.8", "babel-jest": "^29.7.0", "eslint": "^9.31.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-node": "^11.1.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "supertest": "^7.1.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-username/dsv-scraper-nodejs.git"}, "bugs": {"url": "https://github.com/your-username/dsv-scraper-nodejs/issues"}, "homepage": "https://github.com/your-username/dsv-scraper-nodejs#readme"}
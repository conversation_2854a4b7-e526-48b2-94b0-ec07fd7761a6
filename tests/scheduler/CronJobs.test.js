/**
 * Tests für CronJobs/ScheduledScraper
 */
import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';

// Mock dependencies
const mockCron = {
    schedule: jest.fn(() => ({
        destroy: jest.fn(),
        options: { cronExpression: '0 6 * * 0' },
        nextDates: jest.fn(() => [new Date()])
    }))
};

const mockDatabaseManager = {
    connect: jest.fn(),
    disconnect: jest.fn(),
    upsertSchedulerJob: jest.fn(),
    updateSchedulerJobStatus: jest.fn(),
    prisma: {
        scrapeLog: {
            deleteMany: jest.fn()
        },
        notification: {
            deleteMany: jest.fn()
        }
    }
};

const mockDSVScraper = {
    loadConfig: jest.fn(),
    scrapeAllEvents: jest.fn()
};

const mockDataProcessor = {
    processScrapingResults: jest.fn()
};

const mockNotificationService = {
    sendScrapingSuccessNotification: jest.fn(),
    sendScrapingErrorNotification: jest.fn(),
    sendHealthAlertNotification: jest.fn()
};

jest.unstable_mockModule('node-cron', () => ({ default: mockCron }));
jest.unstable_mockModule('../../src/utils/DatabaseManager.js', () => ({
    default: jest.fn(() => mockDatabaseManager)
}));
jest.unstable_mockModule('../../src/scrapers/DSVScraper.js', () => ({
    default: jest.fn(() => mockDSVScraper)
}));
jest.unstable_mockModule('../../src/utils/DataProcessor.js', () => ({
    default: jest.fn(() => mockDataProcessor)
}));
jest.unstable_mockModule('../Notifications.js', () => ({
    default: jest.fn(() => mockNotificationService)
}));

describe('ScheduledScraper', () => {
    let ScheduledScraper;
    let scheduler;
    
    beforeEach(async () => {
        jest.clearAllMocks();
        
        // Setup default mock responses
        mockDatabaseManager.connect.mockResolvedValue();
        mockDSVScraper.loadConfig.mockResolvedValue();
        mockDSVScraper.scrapeAllEvents.mockResolvedValue({
            'event1_2010_M_1': [testHelpers.createMockSwimmerResult()],
            'event2_2010_M_1': []
        });
        mockDataProcessor.processScrapingResults.mockResolvedValue({
            'event1_2010_M_1': { success: true, saved: 1 },
            'event2_2010_M_1': { success: true, saved: 0 }
        });
        mockNotificationService.sendScrapingSuccessNotification.mockResolvedValue();
        mockNotificationService.sendScrapingErrorNotification.mockResolvedValue();
        mockNotificationService.sendHealthAlertNotification.mockResolvedValue();
        
        const module = await import('../../src/scheduler/CronJobs.js');
        ScheduledScraper = module.ScheduledScraper;
        scheduler = new ScheduledScraper();
    });
    
    afterEach(() => {
        if (scheduler) {
            scheduler.stopAllJobs();
        }
    });
    
    test('should create ScheduledScraper instance', () => {
        expect(scheduler).toBeInstanceOf(ScheduledScraper);
        expect(scheduler.isRunning).toBe(false);
        expect(scheduler.scheduledJobs).toBeInstanceOf(Map);
    });
    
    test('should initialize successfully', async () => {
        await scheduler.initialize();
        
        expect(mockDatabaseManager.connect).toHaveBeenCalled();
        expect(mockDSVScraper.loadConfig).toHaveBeenCalled();
        expect(scheduler.db).toBeDefined();
        expect(scheduler.scraper).toBeDefined();
        expect(scheduler.processor).toBeDefined();
        expect(scheduler.notifications).toBeDefined();
    });
    
    test('should handle initialization error', async () => {
        mockDatabaseManager.connect.mockRejectedValue(new Error('Connection failed'));
        
        await expect(scheduler.initialize()).rejects.toThrow('Connection failed');
    });
    
    test('should start all jobs when scheduler is enabled', async () => {
        // Mock config to enable scheduler
        process.env.ENABLE_SCHEDULER = 'true';
        
        await scheduler.startAllJobs();
        
        expect(mockCron.schedule).toHaveBeenCalledTimes(3); // weekly, daily, hourly
        expect(scheduler.scheduledJobs.size).toBe(3);
    });
    
    test('should not start jobs when scheduler is disabled', async () => {
        process.env.ENABLE_SCHEDULER = 'false';
        
        await scheduler.startAllJobs();
        
        expect(mockCron.schedule).not.toHaveBeenCalled();
    });
    
    test('should schedule weekly scraping correctly', () => {
        process.env.SCRAPE_TIME = '06:00';
        process.env.SCRAPE_DAY = 'sunday';
        
        scheduler.scheduleWeeklyScraping();
        
        expect(mockCron.schedule).toHaveBeenCalledWith(
            '0 6 * * 0', // Sunday 06:00
            expect.any(Function),
            expect.objectContaining({
                scheduled: true,
                timezone: expect.any(String)
            })
        );
        
        expect(scheduler.scheduledJobs.has('weekly_scraping')).toBe(true);
    });
    
    test('should schedule daily cleanup correctly', () => {
        scheduler.scheduleDailyCleanup();
        
        expect(mockCron.schedule).toHaveBeenCalledWith(
            '0 2 * * *', // Daily 02:00
            expect.any(Function),
            expect.objectContaining({
                scheduled: true,
                timezone: expect.any(String)
            })
        );
        
        expect(scheduler.scheduledJobs.has('daily_cleanup')).toBe(true);
    });
    
    test('should schedule health check correctly', () => {
        scheduler.scheduleHealthCheck();
        
        expect(mockCron.schedule).toHaveBeenCalledWith(
            '0 * * * *', // Hourly
            expect.any(Function),
            expect.objectContaining({
                scheduled: true,
                timezone: expect.any(String)
            })
        );
        
        expect(scheduler.scheduledJobs.has('health_check')).toBe(true);
    });
    
    test('should execute weekly scraping job successfully', async () => {
        await scheduler.initialize();
        
        await scheduler.weeklyScrapingJob();
        
        expect(mockDatabaseManager.upsertSchedulerJob).toHaveBeenCalled();
        expect(mockDSVScraper.scrapeAllEvents).toHaveBeenCalled();
        expect(mockDataProcessor.processScrapingResults).toHaveBeenCalled();
        expect(mockDatabaseManager.updateSchedulerJobStatus).toHaveBeenCalledWith(
            'weekly_scraping',
            'success',
            expect.any(Number)
        );
        expect(mockNotificationService.sendScrapingSuccessNotification).toHaveBeenCalled();
    });
    
    test('should handle weekly scraping job error', async () => {
        await scheduler.initialize();
        
        const error = new Error('Scraping failed');
        mockDSVScraper.scrapeAllEvents.mockRejectedValue(error);
        
        await scheduler.weeklyScrapingJob();
        
        expect(mockDatabaseManager.updateSchedulerJobStatus).toHaveBeenCalledWith(
            'weekly_scraping',
            'error',
            expect.any(Number),
            error.message
        );
        expect(mockNotificationService.sendScrapingErrorNotification).toHaveBeenCalledWith(
            error,
            expect.any(Number)
        );
    });
    
    test('should skip weekly scraping if already running', async () => {
        scheduler.isRunning = true;
        
        await scheduler.weeklyScrapingJob();
        
        expect(mockDSVScraper.scrapeAllEvents).not.toHaveBeenCalled();
    });
    
    test('should execute daily cleanup job', async () => {
        await scheduler.initialize();
        
        mockDatabaseManager.prisma.scrapeLog.deleteMany.mockResolvedValue({ count: 5 });
        mockDatabaseManager.prisma.notification.deleteMany.mockResolvedValue({ count: 3 });
        
        await scheduler.dailyCleanupJob();
        
        expect(mockDatabaseManager.prisma.scrapeLog.deleteMany).toHaveBeenCalled();
        expect(mockDatabaseManager.prisma.notification.deleteMany).toHaveBeenCalled();
    });
    
    test('should execute health check job successfully', async () => {
        await scheduler.initialize();
        
        mockDatabaseManager.healthCheck = jest.fn().mockResolvedValue({
            status: 'healthy'
        });
        
        await scheduler.healthCheckJob();
        
        expect(mockDatabaseManager.healthCheck).toHaveBeenCalled();
        expect(mockNotificationService.sendHealthAlertNotification).not.toHaveBeenCalled();
    });
    
    test('should send alert on health check failure', async () => {
        await scheduler.initialize();
        
        const unhealthyStatus = {
            status: 'unhealthy',
            error: 'Database connection failed'
        };
        
        mockDatabaseManager.healthCheck = jest.fn().mockResolvedValue(unhealthyStatus);
        
        await scheduler.healthCheckJob();
        
        expect(mockNotificationService.sendHealthAlertNotification).toHaveBeenCalledWith(
            unhealthyStatus
        );
    });
    
    test('should stop all jobs', () => {
        const mockJob1 = { destroy: jest.fn() };
        const mockJob2 = { destroy: jest.fn() };
        
        scheduler.scheduledJobs.set('job1', mockJob1);
        scheduler.scheduledJobs.set('job2', mockJob2);
        
        scheduler.stopAllJobs();
        
        expect(mockJob1.destroy).toHaveBeenCalled();
        expect(mockJob2.destroy).toHaveBeenCalled();
        expect(scheduler.scheduledJobs.size).toBe(0);
    });
    
    test('should convert day name to number correctly', () => {
        expect(scheduler.getDayOfWeek('sunday')).toBe(0);
        expect(scheduler.getDayOfWeek('monday')).toBe(1);
        expect(scheduler.getDayOfWeek('saturday')).toBe(6);
        expect(scheduler.getDayOfWeek('invalid')).toBe(0);
    });
    
    test('should calculate statistics correctly', () => {
        const results = {
            'event1_2010_M_1': [
                testHelpers.createMockSwimmerResult({ ageGroup: '2010', gender: 'M' }),
                testHelpers.createMockSwimmerResult({ ageGroup: '2010', gender: 'M' })
            ],
            'event2_2011_W_2': [
                testHelpers.createMockSwimmerResult({ ageGroup: '2011', gender: 'W' })
            ],
            'event3_2010_M_1': [] // Failed event
        };
        
        const stats = scheduler.calculateStats(results);
        
        expect(stats.totalEvents).toBe(3);
        expect(stats.totalRankings).toBe(3);
        expect(stats.successfulEvents).toBe(2);
        expect(stats.failedEvents).toBe(1);
        expect(stats.ageGroups).toBe(2); // 2010, 2011
        expect(stats.genders).toBe(2); // M, W
    });
});

/**
 * Tests für DSVScraper
 */
import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';

// Mock cheerio
const mockCheerio = {
    load: jest.fn(() => ({
        find: jest.fn(),
        attr: jest.fn(),
        text: jest.fn(),
        html: jest.fn(),
        each: jest.fn()
    }))
};

jest.unstable_mockModule('cheerio', () => ({
    default: mockCheerio
}));

// Mock BaseScraper
const mockBaseScraper = {
    BaseScraper: class MockBaseScraper {
        constructor(configPath) {
            this.configPath = configPath;
            this.config = null;
            this.makeRequest = jest.fn();
            this.normalizeSwimmerName = jest.fn(name => name?.trim() || '');
            this.normalizeClubName = jest.fn(club => club?.trim() || '');
            this.parseTimeToSeconds = jest.fn(time => {
                if (time === '2:15.45') return 135.45;
                if (time === '2:18.32') return 138.32;
                return null;
            });
            this.extractBirthYear = jest.fn(text => {
                if (text === '2010') return 2010;
                if (text === '2011') return 2011;
                return null;
            });
        }
    },
    SwimmerResult: class MockSwimmerResult {
        constructor(data) {
            Object.assign(this, data);
        }
    }
};

jest.unstable_mockModule('../../src/scrapers/BaseScraper.js', () => mockBaseScraper);

describe('DSVScraper', () => {
    let DSVScraper;
    let scraper;
    let mockConfig;
    
    beforeEach(async () => {
        jest.clearAllMocks();
        
        const scraperModule = await import('../../src/scrapers/DSVScraper.js');
        DSVScraper = scraperModule.DSVScraper;
        
        scraper = new DSVScraper('test-config.json');
        
        mockConfig = testHelpers.createMockScrapingConfig();
        
        // Setup default mock responses
        scraper.makeRequest = jest.fn();
        
        const mockHtml = testHelpers.createMockHTMLResponse();
        const mockResponse = { data: mockHtml };
        scraper.makeRequest.mockResolvedValue(mockResponse);
        
        // Setup cheerio mock
        const mockTable = {
            length: 1,
            find: jest.fn(() => ({
                slice: jest.fn(() => ({
                    each: jest.fn((callback) => {
                        // Simulate two table rows
                        const mockRows = [
                            createMockTableRow(['1', 'Max Mustermann', '2010', 'Test SV', '2:15.45', 'Test Wettkampf', '15.01.2024', 'Test Halle']),
                            createMockTableRow(['2', 'Anna Beispiel', '2011', 'Beispiel SC', '2:18.32', 'Test Wettkampf', '15.01.2024', 'Test Halle'])
                        ];
                        mockRows.forEach((row, index) => callback(index, row));
                    })
                }))
            })),
            first: jest.fn(() => mockTable)
        };
        
        const mockCheerioInstance = {
            find: jest.fn((selector) => {
                if (selector === 'input[type="hidden"]') {
                    return {
                        each: jest.fn((callback) => {
                            callback(0, { name: '__VIEWSTATE', value: 'test-viewstate' });
                            callback(1, { name: '__EVENTVALIDATION', value: 'test-validation' });
                        })
                    };
                }
                if (selector === '#ctl00_ContentSection__rankingsDiv table') {
                    return mockTable;
                }
                return { length: 0 };
            }),
            attr: jest.fn((attr) => {
                if (attr === 'name') return '__VIEWSTATE';
                if (attr === 'value') return 'test-value';
                return null;
            })
        };
        
        mockCheerio.load.mockReturnValue(mockCheerioInstance);
    });
    
    function createMockTableRow(cells) {
        return {
            find: jest.fn(() => ({
                length: cells.length,
                text: jest.fn().mockImplementation(function() {
                    const index = Array.from(this.parent().children()).indexOf(this);
                    return cells[index] || '';
                }),
                trim: jest.fn().mockImplementation(function() {
                    return this.text().trim();
                })
            }))
        };
    }
    
    test('should create DSVScraper instance', () => {
        expect(scraper).toBeInstanceOf(DSVScraper);
        expect(scraper.baseUrl).toBe('https://dsvdaten.dsv.de/Modules/Clubs/Index.aspx');
    });
    
    test('should return federation name', () => {
        expect(scraper.getFederationName()).toBe('Deutscher Schwimm-Verband (DSV)');
    });
    
    test('should scrape event successfully', async () => {
        const results = await scraper.scrapeEvent(mockConfig);
        
        expect(results).toBeInstanceOf(Array);
        expect(scraper.makeRequest).toHaveBeenCalledTimes(3); // GET, POST filter, POST data
        
        // Verify the requests were made correctly
        const calls = scraper.makeRequest.mock.calls;
        
        // First call: GET request
        expect(calls[0][0]).toBe('GET');
        expect(calls[0][1]).toContain('StateID=1');
        
        // Second call: POST filter
        expect(calls[1][0]).toBe('POST');
        expect(calls[1][2].headers['Content-Type']).toBe('application/x-www-form-urlencoded');
        
        // Third call: POST data
        expect(calls[2][0]).toBe('POST');
        expect(calls[2][2].data).toContain('_rankingsButton');
    });
    
    test('should handle scraping errors gracefully', async () => {
        scraper.makeRequest.mockRejectedValue(new Error('Network error'));
        
        const results = await scraper.scrapeEvent(mockConfig);
        
        expect(results).toEqual([]);
    });
    
    test('should extract hidden fields correctly', () => {
        const mockHtml = `
            <input type="hidden" name="__VIEWSTATE" value="test-viewstate" />
            <input type="hidden" name="__EVENTVALIDATION" value="test-validation" />
            <input type="text" name="visible-field" value="should-be-ignored" />
        `;
        
        const $ = mockCheerio.load(mockHtml);
        const hiddenFields = scraper._extractHiddenFields($);
        
        expect(hiddenFields).toEqual({
            '__VIEWSTATE': 'test-viewstate',
            '__EVENTVALIDATION': 'test-validation'
        });
    });
    
    test('should build filter payload correctly', () => {
        const payload = scraper._buildFilterPayload(mockConfig);
        
        expect(payload).toEqual({
            'ctl00$ContentSection$_eventDropDown': mockConfig.eventCode,
            'ctl00$ContentSection$_ageGroupDropDown': mockConfig.ageGroup,
            'ctl00$ContentSection$_genderDropDown': mockConfig.gender,
            'ctl00$ContentSection$_timeRangeTextBox': mockConfig.timeRange,
            'ctl00$ContentSection$_courseDropDown': mockConfig.course
        });
    });
    
    test('should parse rankings table with valid data', () => {
        const mockHtml = testHelpers.createMockHTMLResponse();
        
        const results = scraper._parseRankingsTable(mockHtml, mockConfig);
        
        expect(results).toBeInstanceOf(Array);
        // Results length depends on the mock implementation
    });
    
    test('should handle empty rankings table', () => {
        const mockHtml = '<html><body><div id="ctl00_ContentSection__rankingsDiv"></div></body></html>';
        
        const results = scraper._parseRankingsTable(mockHtml, mockConfig);
        
        expect(results).toEqual([]);
    });
    
    test('should parse table row correctly', () => {
        const mockRow = {
            find: jest.fn(() => ({
                length: 8,
                text: jest.fn().mockReturnValueOnce('1')
                    .mockReturnValueOnce('Max Mustermann')
                    .mockReturnValueOnce('2010')
                    .mockReturnValueOnce('Test SV')
                    .mockReturnValueOnce('2:15.45')
                    .mockReturnValueOnce('Test Wettkampf')
                    .mockReturnValueOnce('15.01.2024')
                    .mockReturnValueOnce('Test Halle'),
                trim: jest.fn().mockReturnThis()
            }))
        };
        
        const $ = jest.fn(() => mockRow);
        
        const result = scraper._parseTableRow($(mockRow), mockConfig);
        
        expect(result).toBeInstanceOf(mockBaseScraper.SwimmerResult);
    });
    
    test('should handle invalid table row', () => {
        const mockRow = {
            find: jest.fn(() => ({
                length: 3 // Not enough columns
            }))
        };
        
        const $ = jest.fn(() => mockRow);
        
        const result = scraper._parseTableRow($(mockRow), mockConfig);
        
        expect(result).toBeNull();
    });
    
    test('should handle table row with missing data', () => {
        const mockRow = {
            find: jest.fn(() => ({
                length: 8,
                text: jest.fn().mockReturnValueOnce('') // Empty rank
                    .mockReturnValueOnce('Max Mustermann')
                    .mockReturnValueOnce('2010')
                    .mockReturnValueOnce('Test SV')
                    .mockReturnValueOnce('2:15.45')
                    .mockReturnValueOnce('Test Wettkampf')
                    .mockReturnValueOnce('15.01.2024')
                    .mockReturnValueOnce('Test Halle'),
                trim: jest.fn().mockReturnThis()
            }))
        };
        
        const $ = jest.fn(() => mockRow);
        
        const result = scraper._parseTableRow($(mockRow), mockConfig);
        
        expect(result).toBeNull(); // Should return null for invalid rank
    });
    
    test('should parse date correctly', () => {
        const mockRow = {
            find: jest.fn(() => ({
                length: 8,
                text: jest.fn().mockReturnValueOnce('1')
                    .mockReturnValueOnce('Max Mustermann')
                    .mockReturnValueOnce('2010')
                    .mockReturnValueOnce('Test SV')
                    .mockReturnValueOnce('2:15.45')
                    .mockReturnValueOnce('Test Wettkampf')
                    .mockReturnValueOnce('15.01.2024') // German date format
                    .mockReturnValueOnce('Test Halle'),
                trim: jest.fn().mockReturnThis()
            }))
        };
        
        const $ = jest.fn(() => mockRow);
        
        const result = scraper._parseTableRow($(mockRow), mockConfig);
        
        expect(result.date).toBeInstanceOf(Date);
        expect(result.date.getFullYear()).toBe(2024);
        expect(result.date.getMonth()).toBe(0); // January (0-indexed)
        expect(result.date.getDate()).toBe(15);
    });
    
    test('should handle invalid date format', () => {
        const mockRow = {
            find: jest.fn(() => ({
                length: 8,
                text: jest.fn().mockReturnValueOnce('1')
                    .mockReturnValueOnce('Max Mustermann')
                    .mockReturnValueOnce('2010')
                    .mockReturnValueOnce('Test SV')
                    .mockReturnValueOnce('2:15.45')
                    .mockReturnValueOnce('Test Wettkampf')
                    .mockReturnValueOnce('invalid-date')
                    .mockReturnValueOnce('Test Halle'),
                trim: jest.fn().mockReturnThis()
            }))
        };
        
        const $ = jest.fn(() => mockRow);
        
        const result = scraper._parseTableRow($(mockRow), mockConfig);
        
        expect(result.date).toBeNull();
    });
});

/**
 * Tests für BaseScraper
 */
import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';

// Mock dependencies
jest.unstable_mockModule('puppeteer', () => ({
    default: {
        launch: jest.fn(() => Promise.resolve({
            newPage: jest.fn(() => Promise.resolve({
                setUserAgent: jest.fn(),
                setViewport: jest.fn(),
                close: jest.fn()
            })),
            close: jest.fn()
        }))
    }
}));

jest.unstable_mockModule('axios', () => ({
    default: {
        create: jest.fn(() => ({
            interceptors: {
                request: { use: jest.fn() },
                response: { use: jest.fn() }
            },
            get: jest.fn(),
            post: jest.fn()
        }))
    }
}));

jest.unstable_mockModule('fs/promises', () => ({
    readFile: jest.fn()
}));

describe('BaseScraper', () => {
    let BaseScraper, ScrapingConfig, SwimmerResult;
    let scraper;
    let mockAxios;
    let mockFs;
    
    beforeEach(async () => {
        jest.clearAllMocks();
        
        const scraperModule = await import('../../src/scrapers/BaseScraper.js');
        BaseScraper = scraperModule.BaseScraper;
        ScrapingConfig = scraperModule.ScrapingConfig;
        SwimmerResult = scraperModule.SwimmerResult;
        
        const axiosModule = await import('axios');
        mockAxios = axiosModule.default;
        
        const fsModule = await import('fs/promises');
        mockFs = fsModule;
        
        scraper = new BaseScraper('test-config.json');
    });
    
    afterEach(async () => {
        if (scraper) {
            await scraper.closeBrowser();
        }
    });
    
    test('should create BaseScraper instance', () => {
        expect(scraper).toBeInstanceOf(BaseScraper);
        expect(scraper.configPath).toBe('test-config.json');
        expect(scraper.config).toBeNull();
        expect(scraper.browser).toBeNull();
        expect(scraper.page).toBeNull();
    });
    
    test('should create ScrapingConfig instance', () => {
        const config = new ScrapingConfig({
            eventName: '200m Lagen',
            eventCode: '200L|GL',
            distance: 200,
            stroke: 'Lagen',
            course: 'L',
            ageGroup: '2010',
            gender: 'M',
            season: '2025',
            timeRange: '01.06.2024|31.05.2025',
            regionId: 1
        });
        
        expect(config.eventName).toBe('200m Lagen');
        expect(config.distance).toBe(200);
        expect(config.regionId).toBe(1);
    });
    
    test('should create SwimmerResult instance', () => {
        const result = new SwimmerResult({
            swimmerName: 'Max Mustermann',
            birthYear: 2010,
            club: 'Test SV',
            time: '2:15.45',
            timeSeconds: 135.45,
            rank: 1,
            ageGroup: '2010',
            gender: 'M'
        });
        
        expect(result.swimmerName).toBe('Max Mustermann');
        expect(result.timeSeconds).toBe(135.45);
        expect(result.rank).toBe(1);
    });
    
    test('should load configuration from file', async () => {
        const mockConfig = {
            events: [{ name: 'Test Event', enabled: true }],
            regions: [{ id: 1, name: 'Test Region' }]
        };
        
        mockFs.readFile.mockResolvedValue(JSON.stringify(mockConfig));
        
        await scraper.loadConfig();
        
        expect(scraper.config).toEqual(mockConfig);
        expect(mockFs.readFile).toHaveBeenCalledWith('test-config.json', 'utf8');
    });
    
    test('should handle configuration loading error', async () => {
        mockFs.readFile.mockRejectedValue(new Error('File not found'));
        
        await expect(scraper.loadConfig()).rejects.toThrow('File not found');
    });
    
    test('should setup axios instance', () => {
        expect(mockAxios.create).toHaveBeenCalled();
        expect(scraper.axiosInstance).toBeDefined();
    });
    
    test('should initialize browser', async () => {
        const puppeteer = await import('puppeteer');
        
        await scraper.initBrowser();
        
        expect(puppeteer.default.launch).toHaveBeenCalled();
        expect(scraper.browser).toBeDefined();
        expect(scraper.page).toBeDefined();
    });
    
    test('should close browser', async () => {
        await scraper.initBrowser();
        const mockBrowser = scraper.browser;
        const mockPage = scraper.page;
        
        await scraper.closeBrowser();
        
        expect(mockPage.close).toHaveBeenCalled();
        expect(mockBrowser.close).toHaveBeenCalled();
        expect(scraper.browser).toBeNull();
        expect(scraper.page).toBeNull();
    });
    
    test('should make HTTP request with retry logic', async () => {
        const mockResponse = { data: 'test response' };
        scraper.axiosInstance = {
            get: jest.fn()
                .mockRejectedValueOnce(new Error('Network error'))
                .mockResolvedValue(mockResponse)
        };
        
        const response = await scraper.makeRequest('GET', 'http://test.com');
        
        expect(response).toEqual(mockResponse);
        expect(scraper.axiosInstance.get).toHaveBeenCalledTimes(2);
    });
    
    test('should fail after max retries', async () => {
        const error = new Error('Persistent error');
        scraper.axiosInstance = {
            post: jest.fn().mockRejectedValue(error)
        };
        
        await expect(
            scraper.makeRequest('POST', 'http://test.com')
        ).rejects.toThrow('Persistent error');
        
        expect(scraper.axiosInstance.post).toHaveBeenCalledTimes(1); // MAX_RETRIES = 1 in test env
    });
    
    test('should parse time to seconds correctly', () => {
        expect(scraper.parseTimeToSeconds('2:15.45')).toBe(135.45);
        expect(scraper.parseTimeToSeconds('1:30.00')).toBe(90.0);
        expect(scraper.parseTimeToSeconds('45.67')).toBe(45.67);
        expect(scraper.parseTimeToSeconds('2:05,23')).toBe(125.23); // German format
        expect(scraper.parseTimeToSeconds('')).toBeNull();
        expect(scraper.parseTimeToSeconds(null)).toBeNull();
        expect(scraper.parseTimeToSeconds('invalid')).toBeNull();
    });
    
    test('should extract birth year from text', () => {
        expect(scraper.extractBirthYear('Jahrgang 2010')).toBe(2010);
        expect(scraper.extractBirthYear('2005')).toBe(2005);
        expect(scraper.extractBirthYear('Geboren 1998')).toBe(1998);
        expect(scraper.extractBirthYear('2025')).toBe(null); // Future year
        expect(scraper.extractBirthYear('1985')).toBe(null); // Too old
        expect(scraper.extractBirthYear('')).toBe(null);
        expect(scraper.extractBirthYear(null)).toBe(null);
    });
    
    test('should normalize swimmer name', () => {
        expect(scraper.normalizeSwimmerName('  max mustermann  ')).toBe('Max Mustermann');
        expect(scraper.normalizeSwimmerName('ANNA BEISPIEL')).toBe('Anna Beispiel');
        expect(scraper.normalizeSwimmerName('müller, hans')).toBe('Müller Hans');
        expect(scraper.normalizeSwimmerName('')).toBe('');
        expect(scraper.normalizeSwimmerName(null)).toBe('');
    });
    
    test('should normalize club name', () => {
        expect(scraper.normalizeClubName('  SV Test Verein  ')).toBe('SV Test Verein');
        expect(scraper.normalizeClubName('sc beispiel')).toBe('SC Beispiel');
        expect(scraper.normalizeClubName('')).toBe('');
        expect(scraper.normalizeClubName(null)).toBe('');
    });
    
    test('should wait between requests', async () => {
        const startTime = Date.now();
        await scraper.waitBetweenRequests();
        const endTime = Date.now();
        
        // In test environment, SCRAPING_DELAY is 0, so should be very fast
        expect(endTime - startTime).toBeLessThan(100);
    });
    
    test('should sleep for specified duration', async () => {
        const startTime = Date.now();
        await scraper.sleep(50);
        const endTime = Date.now();
        
        expect(endTime - startTime).toBeGreaterThanOrEqual(45);
        expect(endTime - startTime).toBeLessThan(100);
    });
    
    test('should throw error for abstract methods', async () => {
        await expect(scraper.scrapeEvent({})).rejects.toThrow(
            'scrapeEvent() muss von der abgeleiteten Klasse implementiert werden'
        );
        
        expect(() => scraper.getFederationName()).toThrow(
            'getFederationName() muss von der abgeleiteten Klasse implementiert werden'
        );
    });
    
    test('should scrape all events with configuration', async () => {
        const mockConfig = {
            events: [{ name: 'Test Event', code: 'TEST|GL', distance: 200, stroke: 'Test', course: 'L' }],
            age_groups: [{ year: '2010|2010', name: '15 Jahre - JG 2010' }],
            genders: [{ code: 'M', name: 'Männlich' }],
            regions: [{ id: 1 }],
            seasons: [{ year: '2025', name: 'Saison 2024/2025' }],
            time_ranges: [{ code: 'test-range', name: 'Test Range' }]
        };
        
        mockFs.readFile.mockResolvedValue(JSON.stringify(mockConfig));
        
        // Mock the abstract scrapeEvent method
        scraper.scrapeEvent = jest.fn().mockResolvedValue([
            testHelpers.createMockSwimmerResult()
        ]);
        
        await scraper.loadConfig();
        const results = await scraper.scrapeAllEvents();
        
        expect(results).toBeDefined();
        expect(scraper.scrapeEvent).toHaveBeenCalled();
    });
});

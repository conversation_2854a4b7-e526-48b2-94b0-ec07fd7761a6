/**
 * Tests für DatabaseManager
 */
import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';

// Mock Prisma Client
const mockPrismaClient = {
    $connect: jest.fn(),
    $disconnect: jest.fn(),
    $queryRaw: jest.fn(),
    event: {
        findMany: jest.fn(),
        findFirst: jest.fn(),
        upsert: jest.fn(),
        count: jest.fn()
    },
    region: {
        findMany: jest.fn(),
        findUnique: jest.fn(),
        upsert: jest.fn(),
        count: jest.fn()
    },
    ranking: {
        findMany: jest.fn(),
        create: jest.fn(),
        createMany: jest.fn(),
        deleteMany: jest.fn(),
        count: jest.fn()
    },
    scrapeLog: {
        create: jest.fn(),
        update: jest.fn(),
        findMany: jest.fn()
    },
    schedulerJob: {
        upsert: jest.fn(),
        update: jest.fn()
    },
    $transaction: jest.fn()
};

jest.unstable_mockModule('@prisma/client', () => ({
    PrismaClient: jest.fn(() => mockPrismaClient)
}));

describe('DatabaseManager', () => {
    let DatabaseManager;
    let dbManager;
    
    beforeEach(async () => {
        jest.clearAllMocks();
        
        const module = await import('../../src/utils/DatabaseManager.js');
        DatabaseManager = module.default;
        dbManager = new DatabaseManager();
    });
    
    afterEach(async () => {
        if (dbManager) {
            await dbManager.disconnect();
        }
    });
    
    test('should create DatabaseManager instance', () => {
        expect(dbManager).toBeInstanceOf(DatabaseManager);
        expect(dbManager.prisma).toBeDefined();
        expect(dbManager.isConnected).toBe(false);
    });
    
    test('should connect to database successfully', async () => {
        mockPrismaClient.$connect.mockResolvedValue();
        
        const result = await dbManager.connect();
        
        expect(result).toBe(true);
        expect(dbManager.isConnected).toBe(true);
        expect(mockPrismaClient.$connect).toHaveBeenCalled();
    });
    
    test('should handle connection errors with retry', async () => {
        mockPrismaClient.$connect
            .mockRejectedValueOnce(new Error('Connection failed'))
            .mockResolvedValue();
        
        const result = await dbManager.connect();
        
        expect(result).toBe(true);
        expect(mockPrismaClient.$connect).toHaveBeenCalledTimes(2);
    });
    
    test('should disconnect from database', async () => {
        mockPrismaClient.$disconnect.mockResolvedValue();
        dbManager.isConnected = true;
        
        await dbManager.disconnect();
        
        expect(dbManager.isConnected).toBe(false);
        expect(mockPrismaClient.$disconnect).toHaveBeenCalled();
    });
    
    test('should perform health check successfully', async () => {
        mockPrismaClient.$queryRaw.mockResolvedValue([{ '?column?': 1 }]);
        
        const health = await dbManager.healthCheck();
        
        expect(health.status).toBe('healthy');
        expect(health.timestamp).toBeInstanceOf(Date);
    });
    
    test('should handle health check failure', async () => {
        const error = new Error('Database unavailable');
        mockPrismaClient.$queryRaw.mockRejectedValue(error);
        
        const health = await dbManager.healthCheck();
        
        expect(health.status).toBe('unhealthy');
        expect(health.error).toBe(error.message);
    });
    
    test('should get events with enabled filter', async () => {
        const mockEvents = [
            { id: '1', name: 'Event 1', enabled: true, priority: 1 },
            { id: '2', name: 'Event 2', enabled: true, priority: 2 }
        ];
        mockPrismaClient.event.findMany.mockResolvedValue(mockEvents);
        
        const events = await dbManager.getEvents(true);
        
        expect(events).toEqual(mockEvents);
        expect(mockPrismaClient.event.findMany).toHaveBeenCalledWith({
            where: { enabled: true },
            orderBy: { priority: 'asc' }
        });
    });
    
    test('should get all events when enabledOnly is false', async () => {
        const mockEvents = [
            { id: '1', name: 'Event 1', enabled: true },
            { id: '2', name: 'Event 2', enabled: false }
        ];
        mockPrismaClient.event.findMany.mockResolvedValue(mockEvents);
        
        const events = await dbManager.getEvents(false);
        
        expect(events).toEqual(mockEvents);
        expect(mockPrismaClient.event.findMany).toHaveBeenCalledWith({
            where: {},
            orderBy: { priority: 'asc' }
        });
    });
    
    test('should upsert event', async () => {
        const eventData = {
            name: 'Test Event',
            code: 'TEST|GL',
            distance: 200,
            stroke: 'Test',
            course: 'L'
        };
        const mockEvent = { id: '1', ...eventData };
        mockPrismaClient.event.upsert.mockResolvedValue(mockEvent);
        
        const result = await dbManager.upsertEvent(eventData);
        
        expect(result).toEqual(mockEvent);
        expect(mockPrismaClient.event.upsert).toHaveBeenCalledWith({
            where: { code: eventData.code },
            update: eventData,
            create: eventData
        });
    });
    
    test('should get regions', async () => {
        const mockRegions = [
            { id: 1, name: 'Region 1', enabled: true },
            { id: 2, name: 'Region 2', enabled: true }
        ];
        mockPrismaClient.region.findMany.mockResolvedValue(mockRegions);
        
        const regions = await dbManager.getRegions();
        
        expect(regions).toEqual(mockRegions);
        expect(mockPrismaClient.region.findMany).toHaveBeenCalledWith({
            where: { enabled: true },
            orderBy: { name: 'asc' }
        });
    });
    
    test('should get rankings with filters', async () => {
        const filters = {
            eventId: 'event-1',
            regionId: 1,
            ageGroup: '2010',
            gender: 'M',
            take: 50
        };
        const mockRankings = [
            { id: '1', swimmerName: 'Swimmer 1', timeSeconds: 120.5 },
            { id: '2', swimmerName: 'Swimmer 2', timeSeconds: 125.3 }
        ];
        mockPrismaClient.ranking.findMany.mockResolvedValue(mockRankings);
        
        const rankings = await dbManager.getRankings(filters);
        
        expect(rankings).toEqual(mockRankings);
        expect(mockPrismaClient.ranking.findMany).toHaveBeenCalledWith({
            where: {
                eventId: 'event-1',
                regionId: 1,
                ageGroup: '2010',
                gender: 'M'
            },
            include: {
                event: true,
                region: true
            },
            orderBy: { timeSeconds: 'asc' },
            take: 50
        });
    });
    
    test('should create multiple rankings', async () => {
        const rankingsData = [
            { swimmerName: 'Swimmer 1', timeSeconds: 120.5 },
            { swimmerName: 'Swimmer 2', timeSeconds: 125.3 }
        ];
        const mockResult = { count: 2 };
        mockPrismaClient.ranking.createMany.mockResolvedValue(mockResult);
        
        const result = await dbManager.createManyRankings(rankingsData);
        
        expect(result).toEqual(mockResult);
        expect(mockPrismaClient.ranking.createMany).toHaveBeenCalledWith({
            data: rankingsData,
            skipDuplicates: true
        });
    });
    
    test('should delete rankings for event', async () => {
        const mockResult = { count: 5 };
        mockPrismaClient.ranking.deleteMany.mockResolvedValue(mockResult);
        
        const result = await dbManager.deleteRankingsForEvent(
            'event-1', 1, '2010', 'M', '2025'
        );
        
        expect(result).toEqual(mockResult);
        expect(mockPrismaClient.ranking.deleteMany).toHaveBeenCalledWith({
            where: {
                eventId: 'event-1',
                regionId: 1,
                ageGroup: '2010',
                gender: 'M',
                season: '2025'
            }
        });
    });
    
    test('should create scrape log', async () => {
        const logData = {
            eventName: 'Test Event',
            regionId: 1,
            status: 'success',
            resultCount: 10,
            startTime: new Date(),
            endTime: new Date(),
            duration: 5000
        };
        const mockLog = { id: '1', ...logData };
        mockPrismaClient.scrapeLog.create.mockResolvedValue(mockLog);
        
        const result = await dbManager.createScrapeLog(logData);
        
        expect(result).toEqual(mockLog);
        expect(mockPrismaClient.scrapeLog.create).toHaveBeenCalledWith({
            data: logData
        });
    });
    
    test('should get statistics', async () => {
        mockPrismaClient.ranking.count.mockResolvedValue(100);
        mockPrismaClient.event.count.mockResolvedValue(8);
        mockPrismaClient.region.count.mockResolvedValue(18);
        mockPrismaClient.scrapeLog.count.mockResolvedValue(5);
        mockPrismaClient.ranking.findMany.mockResolvedValue([
            { id: '1', swimmerName: 'Top Swimmer', timeSeconds: 120.5 }
        ]);
        
        const stats = await dbManager.getStatistics();
        
        expect(stats.totalRankings).toBe(100);
        expect(stats.totalEvents).toBe(8);
        expect(stats.totalRegions).toBe(18);
        expect(stats.recentScrapeLogs).toBe(5);
        expect(stats.topPerformers).toHaveLength(1);
        expect(stats.lastUpdated).toBeInstanceOf(Date);
    });
    
    test('should execute transaction', async () => {
        const callback = jest.fn().mockResolvedValue('transaction result');
        mockPrismaClient.$transaction.mockResolvedValue('transaction result');
        
        const result = await dbManager.transaction(callback);
        
        expect(result).toBe('transaction result');
        expect(mockPrismaClient.$transaction).toHaveBeenCalledWith(callback);
    });
});

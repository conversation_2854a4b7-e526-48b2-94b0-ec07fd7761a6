/**
 * Tests für DataProcessor
 */
import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';

describe('DataProcessor', () => {
    let DataProcessor;
    let processor;
    let mockDatabaseManager;
    
    beforeEach(async () => {
        // Mock database manager
        mockDatabaseManager = {
            prisma: {
                event: {
                    findFirst: jest.fn(),
                    create: jest.fn()
                },
                region: {
                    findUnique: jest.fn(),
                    create: jest.fn()
                }
            },
            upsertEvent: jest.fn(),
            upsertRegion: jest.fn(),
            deleteRankingsForEvent: jest.fn(),
            createManyRankings: jest.fn()
        };
        
        const module = await import('../../src/utils/DataProcessor.js');
        DataProcessor = module.DataProcessor;
        processor = new DataProcessor(mockDatabaseManager);
    });
    
    test('should create DataProcessor instance', () => {
        expect(processor).toBeInstanceOf(DataProcessor);
        expect(processor.db).toBe(mockDatabaseManager);
    });
    
    test('should process scraping results successfully', async () => {
        const scrapingResults = {
            '200m Lagen_2010_M_1': [
                testHelpers.createMockSwimmerResult(),
                testHelpers.createMockSwimmerResult({ rank: 2, timeSeconds: 140.5 })
            ],
            '100m Freistil_2010_M_1': []
        };
        
        // Mock successful event processing
        mockDatabaseManager.upsertEvent.mockResolvedValue({ id: 'event-1' });
        mockDatabaseManager.upsertRegion.mockResolvedValue({ id: 1 });
        mockDatabaseManager.deleteRankingsForEvent.mockResolvedValue({ count: 0 });
        mockDatabaseManager.createManyRankings.mockResolvedValue({ count: 2 });
        
        const results = await processor.processScrapingResults(scrapingResults);
        
        expect(results).toHaveProperty('200m Lagen_2010_M_1');
        expect(results).toHaveProperty('100m Freistil_2010_M_1');
        expect(results['200m Lagen_2010_M_1'].success).toBe(true);
        expect(results['200m Lagen_2010_M_1'].saved).toBe(2);
        expect(results['100m Freistil_2010_M_1'].success).toBe(true);
        expect(results['100m Freistil_2010_M_1'].saved).toBe(0);
    });
    
    test('should handle processing errors gracefully', async () => {
        const scrapingResults = {
            'Test Event_2010_M_1': [testHelpers.createMockSwimmerResult()]
        };
        
        mockDatabaseManager.upsertEvent.mockRejectedValue(new Error('Database error'));
        
        const results = await processor.processScrapingResults(scrapingResults);
        
        expect(results['Test Event_2010_M_1'].success).toBe(false);
        expect(results['Test Event_2010_M_1'].error).toBe('Database error');
    });
    
    test('should process event results with rankings', async () => {
        const rankings = [
            testHelpers.createMockSwimmerResult(),
            testHelpers.createMockSwimmerResult({ rank: 2 })
        ];
        
        mockDatabaseManager.upsertEvent.mockResolvedValue({ id: 'event-1' });
        mockDatabaseManager.upsertRegion.mockResolvedValue({ id: 1 });
        mockDatabaseManager.deleteRankingsForEvent.mockResolvedValue({ count: 0 });
        mockDatabaseManager.createManyRankings.mockResolvedValue({ count: 2 });
        
        const result = await processor.processEventResults('Test Event_2010_M_1', rankings);
        
        expect(result.success).toBe(true);
        expect(result.saved).toBe(2);
        expect(mockDatabaseManager.deleteRankingsForEvent).toHaveBeenCalled();
        expect(mockDatabaseManager.createManyRankings).toHaveBeenCalled();
    });
    
    test('should handle empty rankings', async () => {
        const result = await processor.processEventResults('Test Event_2010_M_1', []);
        
        expect(result.success).toBe(true);
        expect(result.saved).toBe(0);
        expect(result.skipped).toBe(0);
    });
    
    test('should extract event info from eventKey', () => {
        const ranking = testHelpers.createMockSwimmerResult();
        const eventInfo = processor.extractEventInfo('200m Lagen_2010_M_1', ranking);
        
        expect(eventInfo.eventName).toBe('200m Lagen');
        expect(eventInfo.ageGroup).toBe('2010');
        expect(eventInfo.gender).toBe('M');
        expect(eventInfo.regionId).toBe(1);
    });
    
    test('should ensure event exists or create new one', async () => {
        const eventInfo = {
            eventName: '200m Lagen',
            ageGroup: '2010',
            gender: 'M',
            regionId: 1
        };
        
        // Mock existing event
        mockDatabaseManager.prisma.event.findFirst.mockResolvedValue({
            id: 'existing-event',
            name: '200m Lagen'
        });
        
        const event = await processor.ensureEvent(eventInfo);
        
        expect(event.id).toBe('existing-event');
        expect(mockDatabaseManager.prisma.event.findFirst).toHaveBeenCalledWith({
            where: { name: '200m Lagen' }
        });
    });
    
    test('should create new event if not exists', async () => {
        const eventInfo = {
            eventName: '200m Lagen',
            ageGroup: '2010',
            gender: 'M',
            regionId: 1
        };
        
        // Mock no existing event
        mockDatabaseManager.prisma.event.findFirst.mockResolvedValue(null);
        mockDatabaseManager.upsertEvent.mockResolvedValue({
            id: 'new-event',
            name: '200m Lagen'
        });
        
        const event = await processor.ensureEvent(eventInfo);
        
        expect(event.id).toBe('new-event');
        expect(mockDatabaseManager.upsertEvent).toHaveBeenCalled();
    });
    
    test('should ensure region exists or create new one', async () => {
        const regionId = 1;
        
        // Mock existing region
        mockDatabaseManager.prisma.region.findUnique.mockResolvedValue({
            id: 1,
            name: 'Test Region'
        });
        
        const region = await processor.ensureRegion(regionId);
        
        expect(region.id).toBe(1);
        expect(mockDatabaseManager.prisma.region.findUnique).toHaveBeenCalledWith({
            where: { id: 1 }
        });
    });
    
    test('should create new region if not exists', async () => {
        const regionId = 99;
        
        // Mock no existing region
        mockDatabaseManager.prisma.region.findUnique.mockResolvedValue(null);
        mockDatabaseManager.upsertRegion.mockResolvedValue({
            id: 99,
            name: 'Region 99'
        });
        
        const region = await processor.ensureRegion(regionId);
        
        expect(region.id).toBe(99);
        expect(mockDatabaseManager.upsertRegion).toHaveBeenCalledWith({
            id: 99,
            name: 'Region 99',
            abbreviation: 'R99',
            enabled: true
        });
    });
    
    test('should generate event code correctly', () => {
        expect(processor.generateEventCode('200m Freistil')).toBe('200F|GL');
        expect(processor.generateEventCode('100m Brust')).toBe('100B|GL');
        expect(processor.generateEventCode('50m Rücken')).toBe('50R|GL');
        expect(processor.generateEventCode('100m Schmetterling')).toBe('100S|GL');
        expect(processor.generateEventCode('200m Lagen')).toBe('200L|GL');
        expect(processor.generateEventCode('Unknown Event')).toBe('0X|GL');
    });
    
    test('should extract distance from event name', () => {
        expect(processor.extractDistance('200m Lagen')).toBe(200);
        expect(processor.extractDistance('50m Freistil')).toBe(50);
        expect(processor.extractDistance('1500m Freistil')).toBe(1500);
        expect(processor.extractDistance('No Distance Event')).toBe(0);
    });
    
    test('should extract stroke from event name', () => {
        expect(processor.extractStroke('200m Freistil')).toBe('Freistil');
        expect(processor.extractStroke('100m Brust')).toBe('Brust');
        expect(processor.extractStroke('50m Rücken')).toBe('Rücken');
        expect(processor.extractStroke('100m Schmetterling')).toBe('Schmetterling');
        expect(processor.extractStroke('200m Lagen')).toBe('Lagen');
        expect(processor.extractStroke('Unknown Event')).toBe('Unbekannt');
    });
    
    test('should validate ranking data', () => {
        const validRanking = testHelpers.createMockSwimmerResult();
        const errors = processor.validateRankingData(validRanking);
        expect(errors).toHaveLength(0);
        
        const invalidRanking = {
            swimmerName: '',
            time: '',
            timeSeconds: 0,
            rank: 0,
            ageGroup: '',
            gender: 'X'
        };
        
        const invalidErrors = processor.validateRankingData(invalidRanking);
        expect(invalidErrors.length).toBeGreaterThan(0);
        expect(invalidErrors).toContain('Schwimmername ist erforderlich');
        expect(invalidErrors).toContain('Zeit ist erforderlich');
        expect(invalidErrors).toContain('Zeit in Sekunden muss größer als 0 sein');
        expect(invalidErrors).toContain('Rang muss größer als 0 sein');
        expect(invalidErrors).toContain('Altersgruppe ist erforderlich');
        expect(invalidErrors).toContain('Geschlecht muss M oder W sein');
    });
    
    test('should clean ranking data', () => {
        const dirtyRanking = {
            swimmerName: '  Max Mustermann  ',
            club: '  Test SV  ',
            time: '  2:15.45  ',
            competition: '  Test Wettkampf  ',
            location: '  Test Halle  '
        };
        
        const cleanRanking = processor.cleanRankingData(dirtyRanking);
        
        expect(cleanRanking.swimmerName).toBe('Max Mustermann');
        expect(cleanRanking.club).toBe('Test SV');
        expect(cleanRanking.time).toBe('2:15.45');
        expect(cleanRanking.competition).toBe('Test Wettkampf');
        expect(cleanRanking.location).toBe('Test Halle');
    });
    
    test('should handle null values in clean ranking data', () => {
        const rankingWithNulls = {
            swimmerName: null,
            club: null,
            time: null,
            competition: null,
            location: null
        };
        
        const cleanRanking = processor.cleanRankingData(rankingWithNulls);
        
        expect(cleanRanking.club).toBe('Unbekannt');
        expect(cleanRanking.competition).toBeNull();
        expect(cleanRanking.location).toBeNull();
    });
});

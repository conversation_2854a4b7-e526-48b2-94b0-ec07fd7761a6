/**
 * Tests für Logger Module
 */
import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import fs from 'fs';
import path from 'path';

// Mock winston
const mockWinston = {
    createLogger: jest.fn(() => ({
        info: jest.fn(),
        error: jest.fn(),
        warn: jest.fn(),
        debug: jest.fn(),
        child: jest.fn(() => mockWinston.createLogger())
    })),
    format: {
        combine: jest.fn(),
        timestamp: jest.fn(),
        errors: jest.fn(),
        printf: jest.fn(),
        colorize: jest.fn()
    },
    transports: {
        Console: jest.fn(),
        File: jest.fn()
    }
};

const mockDailyRotateFile = jest.fn();

jest.unstable_mockModule('winston', () => ({ default: mockWinston }));
jest.unstable_mockModule('winston-daily-rotate-file', () => ({ default: mockDailyRotateFile }));

describe('Logger Module', () => {
    let logger, webLogger, scraperLogger, schedulerLogger, databaseLogger;
    let logScrapingStart, logScrapingSuccess, logScrapingError;
    let logDatabaseOperation, logWebRequest, logSchedulerJob;
    
    beforeEach(async () => {
        jest.clearAllMocks();
        
        const loggerModule = await import('../../src/utils/logger.js');
        logger = loggerModule.default;
        webLogger = loggerModule.webLogger;
        scraperLogger = loggerModule.scraperLogger;
        schedulerLogger = loggerModule.schedulerLogger;
        databaseLogger = loggerModule.databaseLogger;
        logScrapingStart = loggerModule.logScrapingStart;
        logScrapingSuccess = loggerModule.logScrapingSuccess;
        logScrapingError = loggerModule.logScrapingError;
        logDatabaseOperation = loggerModule.logDatabaseOperation;
        logWebRequest = loggerModule.logWebRequest;
        logSchedulerJob = loggerModule.logSchedulerJob;
    });
    
    test('should create logger with correct configuration', () => {
        expect(mockWinston.createLogger).toHaveBeenCalled();
    });
    
    test('should create specialized loggers', () => {
        expect(webLogger).toBeDefined();
        expect(scraperLogger).toBeDefined();
        expect(schedulerLogger).toBeDefined();
        expect(databaseLogger).toBeDefined();
    });
    
    test('should log scraping start', () => {
        const eventName = '200m Lagen';
        const config = testHelpers.createMockScrapingConfig();
        
        logScrapingStart(eventName, config);
        
        expect(scraperLogger.info).toHaveBeenCalledWith('🚀 Starte Scraping', {
            event: eventName,
            region: config.regionId,
            ageGroup: config.ageGroup,
            gender: config.gender
        });
    });
    
    test('should log scraping success', () => {
        const eventName = '200m Lagen';
        const resultCount = 25;
        const duration = 5000;
        
        logScrapingSuccess(eventName, resultCount, duration);
        
        expect(scraperLogger.info).toHaveBeenCalledWith('✅ Scraping erfolgreich', {
            event: eventName,
            resultCount,
            duration: '5000ms'
        });
    });
    
    test('should log scraping error', () => {
        const eventName = '200m Lagen';
        const error = new Error('Test error');
        const config = testHelpers.createMockScrapingConfig();
        
        logScrapingError(eventName, error, config);
        
        expect(scraperLogger.error).toHaveBeenCalledWith('❌ Scraping fehlgeschlagen', {
            event: eventName,
            error: error.message,
            stack: error.stack,
            config
        });
    });
    
    test('should log database operation', () => {
        const operation = 'CREATE';
        const table = 'rankings';
        const data = { swimmerName: 'Test Swimmer' };
        
        logDatabaseOperation(operation, table, data);
        
        expect(databaseLogger.debug).toHaveBeenCalledWith('📊 Datenbankoperation', {
            operation,
            table,
            data: ['swimmerName']
        });
    });
    
    test('should log web request', () => {
        const req = {
            method: 'GET',
            url: '/api/rankings',
            get: jest.fn(() => 'Mozilla/5.0'),
            ip: '127.0.0.1'
        };
        const res = { statusCode: 200 };
        const duration = 150;
        
        logWebRequest(req, res, duration);
        
        expect(webLogger.info).toHaveBeenCalledWith('🌐 HTTP Request', {
            method: 'GET',
            url: '/api/rankings',
            status: 200,
            duration: '150ms',
            userAgent: 'Mozilla/5.0',
            ip: '127.0.0.1'
        });
    });
    
    test('should log scheduler job', () => {
        const jobName = 'weekly_scraping';
        const status = 'success';
        const details = { duration: '5000ms' };
        
        logSchedulerJob(jobName, status, details);
        
        expect(schedulerLogger.info).toHaveBeenCalledWith('⏰ Scheduler Job: weekly_scraping', {
            status,
            details
        });
    });
    
    test('should handle database operation with string data', () => {
        const operation = 'DELETE';
        const table = 'rankings';
        const data = 'test-id';
        
        logDatabaseOperation(operation, table, data);
        
        expect(databaseLogger.debug).toHaveBeenCalledWith('📊 Datenbankoperation', {
            operation,
            table,
            data: 'test-id'
        });
    });
    
    test('should handle web request without user agent', () => {
        const req = {
            method: 'POST',
            url: '/api/rankings',
            get: jest.fn(() => undefined),
            ip: '***********'
        };
        const res = { statusCode: 201 };
        const duration = 250;
        
        logWebRequest(req, res, duration);
        
        expect(webLogger.info).toHaveBeenCalledWith('🌐 HTTP Request', {
            method: 'POST',
            url: '/api/rankings',
            status: 201,
            duration: '250ms',
            userAgent: undefined,
            ip: '***********'
        });
    });
});

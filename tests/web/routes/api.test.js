/**
 * Tests für API Routes
 */
import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import request from 'supertest';
import express from 'express';

// Mock database manager
const mockDatabaseManager = {
    getRankings: jest.fn(),
    getEvents: jest.fn(),
    getRegions: jest.fn(),
    getStatistics: jest.fn(),
    getRecentScrapeLogs: jest.fn(),
    prisma: {
        ranking: {
            count: jest.fn()
        }
    }
};

describe('API Routes', () => {
    let app;
    let apiRoutes;
    
    beforeEach(async () => {
        jest.clearAllMocks();
        
        // Create test app
        app = express();
        app.use(express.json());
        
        // Add database middleware
        app.use((req, res, next) => {
            req.db = mockDatabaseManager;
            next();
        });
        
        // Import and use API routes
        const routesModule = await import('../../../src/web/routes/api.js');
        apiRoutes = routesModule.default;
        app.use('/api', apiRoutes);
        
        // Setup default mock responses
        mockDatabaseManager.getRankings.mockResolvedValue([]);
        mockDatabaseManager.getEvents.mockResolvedValue([]);
        mockDatabaseManager.getRegions.mockResolvedValue([]);
        mockDatabaseManager.getStatistics.mockResolvedValue({
            totalRankings: 100,
            totalEvents: 8,
            totalRegions: 18
        });
        mockDatabaseManager.getRecentScrapeLogs.mockResolvedValue([]);
        mockDatabaseManager.prisma.ranking.count.mockResolvedValue(0);
    });
    
    describe('GET /api/rankings', () => {
        test('should return rankings without filters', async () => {
            const mockRankings = [
                { id: '1', swimmerName: 'Swimmer 1', timeSeconds: 120.5 },
                { id: '2', swimmerName: 'Swimmer 2', timeSeconds: 125.3 }
            ];
            
            mockDatabaseManager.getRankings.mockResolvedValue(mockRankings);
            mockDatabaseManager.prisma.ranking.count.mockResolvedValue(2);
            
            const response = await request(app)
                .get('/api/rankings')
                .expect(200);
            
            expect(response.body.data).toEqual(mockRankings);
            expect(response.body.pagination).toMatchObject({
                page: 1,
                limit: 100,
                total: 2,
                pages: 1
            });
        });
        
        test('should return rankings with filters', async () => {
            const mockRankings = [
                { id: '1', swimmerName: 'Swimmer 1', eventId: 'event-1' }
            ];
            
            mockDatabaseManager.getRankings.mockResolvedValue(mockRankings);
            mockDatabaseManager.prisma.ranking.count.mockResolvedValue(1);
            
            const response = await request(app)
                .get('/api/rankings')
                .query({
                    eventId: 'event-1',
                    regionId: '1',
                    ageGroup: '2010',
                    gender: 'M',
                    limit: '50',
                    page: '2'
                })
                .expect(200);
            
            expect(mockDatabaseManager.getRankings).toHaveBeenCalledWith({
                eventId: 'event-1',
                regionId: 1,
                ageGroup: '2010',
                gender: 'M',
                take: 50,
                skip: 50
            });
            
            expect(response.body.filters).toMatchObject({
                eventId: 'event-1',
                regionId: '1',
                ageGroup: '2010',
                gender: 'M'
            });
        });
        
        test('should validate query parameters', async () => {
            const response = await request(app)
                .get('/api/rankings')
                .query({
                    regionId: 'invalid',
                    gender: 'X',
                    limit: '2000'
                })
                .expect(400);
            
            expect(response.body.error).toBe('Validierungsfehler');
            expect(response.body.details).toBeInstanceOf(Array);
        });
        
        test('should handle database errors', async () => {
            mockDatabaseManager.getRankings.mockRejectedValue(new Error('Database error'));
            
            const response = await request(app)
                .get('/api/rankings')
                .expect(500);
            
            expect(response.body.error).toBe('Fehler beim Abrufen der Rankings');
        });
    });
    
    describe('GET /api/rankings/top', () => {
        test('should return top rankings', async () => {
            const mockTopRankings = [
                { id: '1', swimmerName: 'Top Swimmer', timeSeconds: 115.2 }
            ];
            
            mockDatabaseManager.getRankings.mockResolvedValue(mockTopRankings);
            
            const response = await request(app)
                .get('/api/rankings/top')
                .query({ limit: '5' })
                .expect(200);
            
            expect(response.body.data).toEqual(mockTopRankings);
            expect(response.body.count).toBe(1);
            
            expect(mockDatabaseManager.getRankings).toHaveBeenCalledWith({
                take: 5
            });
        });
        
        test('should validate top rankings parameters', async () => {
            const response = await request(app)
                .get('/api/rankings/top')
                .query({ limit: '200' }) // Over maximum
                .expect(400);
            
            expect(response.body.error).toBe('Validierungsfehler');
        });
    });
    
    describe('GET /api/events', () => {
        test('should return events', async () => {
            const mockEvents = [
                { id: '1', name: 'Event 1', enabled: true },
                { id: '2', name: 'Event 2', enabled: true }
            ];
            
            mockDatabaseManager.getEvents.mockResolvedValue(mockEvents);
            
            const response = await request(app)
                .get('/api/events')
                .expect(200);
            
            expect(response.body.data).toEqual(mockEvents);
            expect(response.body.count).toBe(2);
        });
        
        test('should handle events database error', async () => {
            mockDatabaseManager.getEvents.mockRejectedValue(new Error('Events error'));
            
            const response = await request(app)
                .get('/api/events')
                .expect(500);
            
            expect(response.body.error).toBe('Fehler beim Abrufen der Events');
        });
    });
    
    describe('GET /api/regions', () => {
        test('should return regions', async () => {
            const mockRegions = [
                { id: 1, name: 'Region 1', enabled: true },
                { id: 2, name: 'Region 2', enabled: true }
            ];
            
            mockDatabaseManager.getRegions.mockResolvedValue(mockRegions);
            
            const response = await request(app)
                .get('/api/regions')
                .expect(200);
            
            expect(response.body.data).toEqual(mockRegions);
            expect(response.body.count).toBe(2);
        });
    });
    
    describe('GET /api/statistics', () => {
        test('should return statistics', async () => {
            const mockStats = {
                totalRankings: 1000,
                totalEvents: 8,
                totalRegions: 18,
                lastUpdated: new Date()
            };
            
            mockDatabaseManager.getStatistics.mockResolvedValue(mockStats);
            
            const response = await request(app)
                .get('/api/statistics')
                .expect(200);
            
            expect(response.body).toEqual(mockStats);
        });
    });
    
    describe('GET /api/scrape-logs', () => {
        test('should return scrape logs', async () => {
            const mockLogs = [
                { id: '1', eventName: 'Test Event', status: 'success' },
                { id: '2', eventName: 'Test Event 2', status: 'error' }
            ];
            
            mockDatabaseManager.getRecentScrapeLogs.mockResolvedValue(mockLogs);
            
            const response = await request(app)
                .get('/api/scrape-logs')
                .query({ limit: '100' })
                .expect(200);
            
            expect(response.body.data).toEqual(mockLogs);
            expect(response.body.count).toBe(2);
            
            expect(mockDatabaseManager.getRecentScrapeLogs).toHaveBeenCalledWith(100);
        });
        
        test('should validate scrape logs limit', async () => {
            const response = await request(app)
                .get('/api/scrape-logs')
                .query({ limit: '500' }) // Over maximum
                .expect(400);
            
            expect(response.body.error).toBe('Validierungsfehler');
        });
    });
    
    describe('GET /api/export/csv', () => {
        test('should export rankings as CSV', async () => {
            const mockRankings = [
                {
                    rank: 1,
                    swimmerName: 'Max Mustermann',
                    birthYear: 2010,
                    club: 'Test SV',
                    time: '2:15.45',
                    ageGroup: '2010',
                    gender: 'M',
                    competition: 'Test Wettkampf',
                    date: new Date('2024-01-15'),
                    location: 'Test Halle',
                    event: { name: '200m Lagen' },
                    region: { name: 'Test Region' }
                }
            ];
            
            mockDatabaseManager.getRankings.mockResolvedValue(mockRankings);
            
            const response = await request(app)
                .get('/api/export/csv')
                .expect(200);
            
            expect(response.headers['content-type']).toContain('text/csv');
            expect(response.headers['content-disposition']).toContain('attachment');
            expect(response.text).toContain('Rang,Name,Geburtsjahr');
            expect(response.text).toContain('Max Mustermann');
        });
        
        test('should export CSV with filters', async () => {
            mockDatabaseManager.getRankings.mockResolvedValue([]);
            
            const response = await request(app)
                .get('/api/export/csv')
                .query({
                    eventId: 'event-1',
                    regionId: '1',
                    gender: 'M'
                })
                .expect(200);
            
            expect(mockDatabaseManager.getRankings).toHaveBeenCalledWith({
                eventId: 'event-1',
                regionId: 1,
                gender: 'M',
                take: 10000
            });
        });
        
        test('should validate CSV export parameters', async () => {
            const response = await request(app)
                .get('/api/export/csv')
                .query({ gender: 'X' })
                .expect(400);
            
            expect(response.body.error).toBe('Validierungsfehler');
        });
    });
});

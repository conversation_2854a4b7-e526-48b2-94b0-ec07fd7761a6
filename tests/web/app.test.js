/**
 * Tests für Express Web Application
 */
import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import request from 'supertest';

// Mock dependencies
const mockDatabaseManager = {
    connect: jest.fn(),
    disconnect: jest.fn(),
    healthCheck: jest.fn(),
    getEvents: jest.fn(),
    getRegions: jest.fn(),
    getRankings: jest.fn(),
    getStatistics: jest.fn(),
    getRecentScrapeLogs: jest.fn(),
    prisma: {
        ranking: {
            count: jest.fn()
        }
    }
};

jest.unstable_mockModule('../../src/utils/DatabaseManager.js', () => ({
    default: jest.fn(() => mockDatabaseManager)
}));

describe('Express Web Application', () => {
    let app;
    let createApp;
    
    beforeEach(async () => {
        jest.clearAllMocks();
        
        // Reset database manager mocks
        mockDatabaseManager.connect.mockResolvedValue();
        mockDatabaseManager.healthCheck.mockResolvedValue({
            status: 'healthy',
            timestamp: new Date()
        });
        mockDatabaseManager.getEvents.mockResolvedValue([]);
        mockDatabaseManager.getRegions.mockResolvedValue([]);
        mockDatabaseManager.getRankings.mockResolvedValue([]);
        mockDatabaseManager.getStatistics.mockResolvedValue({
            totalRankings: 0,
            totalEvents: 0,
            totalRegions: 0,
            recentScrapeLogs: 0,
            topPerformers: [],
            lastUpdated: new Date()
        });
        mockDatabaseManager.getRecentScrapeLogs.mockResolvedValue([]);
        mockDatabaseManager.prisma.ranking.count.mockResolvedValue(0);
        
        const appModule = await import('../../src/web/app.js');
        createApp = appModule.createApp;
        app = createApp();
    });
    
    afterEach(() => {
        if (app && app.locals && app.locals.db) {
            app.locals.db.disconnect();
        }
    });
    
    test('should create Express app', () => {
        expect(app).toBeDefined();
        expect(typeof app).toBe('function');
    });
    
    test('should respond to health check', async () => {
        const response = await request(app)
            .get('/health')
            .expect(200);
        
        expect(response.body).toMatchObject({
            status: 'healthy',
            environment: 'test',
            database: {
                status: 'healthy'
            }
        });
        
        expect(response.body.timestamp).toBeDefined();
        expect(response.body.uptime).toBeDefined();
        expect(response.body.memory).toBeDefined();
    });
    
    test('should handle health check database error', async () => {
        mockDatabaseManager.healthCheck.mockResolvedValue({
            status: 'unhealthy',
            error: 'Connection failed'
        });
        
        const response = await request(app)
            .get('/health')
            .expect(503);
        
        expect(response.body.status).toBe('unhealthy');
    });
    
    test('should serve dashboard page', async () => {
        const response = await request(app)
            .get('/')
            .expect(200);
        
        expect(mockDatabaseManager.getStatistics).toHaveBeenCalled();
        expect(mockDatabaseManager.getEvents).toHaveBeenCalled();
        expect(mockDatabaseManager.getRegions).toHaveBeenCalled();
    });
    
    test('should handle database connection error', async () => {
        mockDatabaseManager.connect.mockRejectedValue(new Error('Database connection failed'));
        
        const response = await request(app)
            .get('/')
            .expect(500);
        
        expect(response.body.error).toBe('Datenbankverbindung fehlgeschlagen');
    });
    
    test('should apply CORS headers', async () => {
        const response = await request(app)
            .options('/api/rankings')
            .expect(204);
        
        expect(response.headers['access-control-allow-origin']).toBeDefined();
    });
    
    test('should apply security headers', async () => {
        const response = await request(app)
            .get('/health')
            .expect(200);
        
        expect(response.headers['x-content-type-options']).toBe('nosniff');
        expect(response.headers['x-frame-options']).toBe('DENY');
    });
    
    test('should handle 404 for unknown routes', async () => {
        const response = await request(app)
            .get('/unknown-route')
            .expect(404);
        
        // Should render error page for non-API routes
        expect(response.text).toContain('Seite nicht gefunden');
    });
    
    test('should handle 404 for unknown API routes', async () => {
        const response = await request(app)
            .get('/api/unknown-endpoint')
            .expect(404);
        
        expect(response.body.error).toBe('API Endpoint nicht gefunden');
        expect(response.body.path).toBe('/api/unknown-endpoint');
    });
    
    test('should handle application errors', async () => {
        // Force an error by making database throw
        mockDatabaseManager.getStatistics.mockRejectedValue(new Error('Database error'));
        
        const response = await request(app)
            .get('/')
            .expect(500);
        
        expect(response.text).toContain('Fehler');
    });
    
    test('should compress responses', async () => {
        const response = await request(app)
            .get('/health')
            .set('Accept-Encoding', 'gzip')
            .expect(200);
        
        // Compression middleware should be applied
        expect(response.headers['content-encoding']).toBeDefined();
    });
    
    test('should serve static files', async () => {
        // This would require actual static files to exist
        const response = await request(app)
            .get('/static/nonexistent.css')
            .expect(404);
        
        // Should still handle the request through static middleware
    });
    
    test('should parse JSON body', async () => {
        // This would be tested with a POST endpoint that accepts JSON
        // For now, just verify the middleware is set up
        expect(app._router).toBeDefined();
    });
    
    test('should parse URL-encoded body', async () => {
        // This would be tested with a POST endpoint that accepts form data
        // For now, just verify the middleware is set up
        expect(app._router).toBeDefined();
    });
    
    test('should log requests', async () => {
        const response = await request(app)
            .get('/health')
            .expect(200);
        
        // Request logging middleware should be applied
        // Actual logging is mocked in setup
    });
    
    test('should set up database middleware', async () => {
        const response = await request(app)
            .get('/health')
            .expect(200);
        
        expect(mockDatabaseManager.connect).toHaveBeenCalled();
    });
    
    test('should handle database middleware error', async () => {
        mockDatabaseManager.connect.mockRejectedValue(new Error('Connection failed'));
        
        const response = await request(app)
            .get('/health')
            .expect(500);
        
        expect(response.body.error).toBe('Datenbankverbindung fehlgeschlagen');
    });
    
    test('should set view engine to EJS', () => {
        expect(app.get('view engine')).toBe('ejs');
    });
    
    test('should trust proxy', () => {
        expect(app.get('trust proxy')).toBe(1);
    });
});

/**
 * Jest Setup für DSV Scraper Tests
 */
import { jest } from '@jest/globals';

// Environment Setup
process.env.NODE_ENV = 'test';
process.env.DATABASE_TYPE = 'sqlite';
process.env.DATABASE_URL = 'file:./test.db';
process.env.LOG_LEVEL = 'error';
process.env.ENABLE_SCHEDULER = 'false';
process.env.SCRAPING_DELAY = '0';
process.env.MAX_RETRIES = '1';

// Global Test Timeout
jest.setTimeout(30000);

// Mock External Services
global.mockAxios = {
  get: jest.fn(),
  post: jest.fn(),
  create: jest.fn(() => global.mockAxios)
};

global.mockPuppeteer = {
  launch: jest.fn(),
  newPage: jest.fn(),
  close: jest.fn(),
  setUserAgent: jest.fn(),
  setViewport: jest.fn(),
  goto: jest.fn(),
  content: jest.fn()
};

// Mock Nodemailer
global.mockNodemailer = {
  createTransporter: jest.fn(() => ({
    sendMail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' })
  }))
};

// Mock Appwrite
global.mockAppwrite = {
  Client: jest.fn(() => ({
    setEndpoint: jest.fn(),
    setProject: jest.fn(),
    setKey: jest.fn()
  })),
  Databases: jest.fn(() => ({
    listDocuments: jest.fn(),
    getDocument: jest.fn(),
    createDocument: jest.fn(),
    updateDocument: jest.fn(),
    deleteDocument: jest.fn()
  })),
  Query: {
    equal: jest.fn(),
    limit: jest.fn(),
    offset: jest.fn(),
    orderAsc: jest.fn(),
    orderDesc: jest.fn()
  }
};

// Console Mocking für saubere Test-Ausgabe
const originalConsole = { ...console };

beforeEach(() => {
  // Reset all mocks
  jest.clearAllMocks();
  
  // Mock console methods für saubere Test-Ausgabe
  console.log = jest.fn();
  console.info = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterEach(() => {
  // Restore console
  Object.assign(console, originalConsole);
});

// Global Test Helpers
global.testHelpers = {
  // Mock Swimmer Result
  createMockSwimmerResult: (overrides = {}) => ({
    swimmerName: 'Max Mustermann',
    birthYear: 2010,
    club: 'Test Schwimmverein',
    time: '2:15.45',
    timeSeconds: 135.45,
    rank: 1,
    ageGroup: '2010',
    gender: 'M',
    competition: 'Test Wettkampf',
    date: new Date('2024-01-15'),
    location: 'Test Schwimmhalle',
    ...overrides
  }),
  
  // Mock Scraping Config
  createMockScrapingConfig: (overrides = {}) => ({
    eventName: '200m Lagen',
    eventCode: '200L|GL',
    distance: 200,
    stroke: 'Lagen',
    course: 'L',
    ageGroup: '2010',
    gender: 'M',
    season: '2025',
    timeRange: '01.06.2024|31.05.2025',
    regionId: 1,
    ...overrides
  }),
  
  // Mock HTML Response
  createMockHTMLResponse: () => `
    <html>
      <body>
        <div id="ctl00_ContentSection__rankingsDiv">
          <table>
            <tr><th>Rang</th><th>Name</th><th>Jahrgang</th><th>Verein</th><th>Zeit</th><th>Wettkampf</th><th>Datum</th><th>Ort</th></tr>
            <tr>
              <td>1</td>
              <td>Max Mustermann</td>
              <td>2010</td>
              <td>Test SV</td>
              <td>2:15.45</td>
              <td>Test Wettkampf</td>
              <td>15.01.2024</td>
              <td>Test Halle</td>
            </tr>
            <tr>
              <td>2</td>
              <td>Anna Beispiel</td>
              <td>2011</td>
              <td>Beispiel SC</td>
              <td>2:18.32</td>
              <td>Test Wettkampf</td>
              <td>15.01.2024</td>
              <td>Test Halle</td>
            </tr>
          </table>
        </div>
        <input type="hidden" name="__VIEWSTATE" value="test-viewstate" />
        <input type="hidden" name="__EVENTVALIDATION" value="test-validation" />
      </body>
    </html>
  `,
  
  // Wait Helper
  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Database Test Helpers
  cleanDatabase: async (db) => {
    if (db && db.prisma) {
      await db.prisma.ranking.deleteMany();
      await db.prisma.scrapeLog.deleteMany();
      await db.prisma.event.deleteMany();
      await db.prisma.region.deleteMany();
    }
  },
  
  // Create Test Data
  createTestEvent: async (db, overrides = {}) => {
    return await db.prisma.event.create({
      data: {
        name: '200m Lagen Test',
        code: '200L|GL',
        distance: 200,
        stroke: 'Lagen',
        course: 'L',
        enabled: true,
        priority: 1,
        ...overrides
      }
    });
  },
  
  createTestRegion: async (db, overrides = {}) => {
    return await db.prisma.region.create({
      data: {
        id: 1,
        name: 'Test Region',
        abbreviation: 'TEST',
        enabled: true,
        ...overrides
      }
    });
  }
};

// Error Handling für unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

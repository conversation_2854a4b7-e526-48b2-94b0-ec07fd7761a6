/**
 * Integration Tests für Scraping Workflow
 */
import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';

describe('Scraping Integration Tests', () => {
    let DSVScraper;
    let DataProcessor;
    let DatabaseManager;
    let scraper;
    let processor;
    let db;
    
    beforeEach(async () => {
        jest.clearAllMocks();
        
        // Import modules
        const scraperModule = await import('../../src/scrapers/DSVScraper.js');
        const processorModule = await import('../../src/utils/DataProcessor.js');
        const dbModule = await import('../../src/utils/DatabaseManager.js');
        
        DSVScraper = scraperModule.DSVScraper;
        DataProcessor = processorModule.DataProcessor;
        DatabaseManager = dbModule.default;
        
        // Create instances
        db = new DatabaseManager();
        processor = new DataProcessor(db);
        scraper = new DSVScraper('src/scrapers/config/events.json');
        
        // Mock database operations for integration tests
        db.connect = jest.fn().mockResolvedValue(true);
        db.disconnect = jest.fn().mockResolvedValue();
        db.upsertEvent = jest.fn().mockResolvedValue({ id: 'test-event-1' });
        db.upsertRegion = jest.fn().mockResolvedValue({ id: 1 });
        db.deleteRankingsForEvent = jest.fn().mockResolvedValue({ count: 0 });
        db.createManyRankings = jest.fn().mockResolvedValue({ count: 2 });
        
        // Mock scraper methods for integration tests
        scraper.loadConfig = jest.fn().mockResolvedValue();
        scraper.config = {
            events: [{ name: '200m Lagen', code: '200L|GL', enabled: true }],
            age_groups: [{ year: '2010', enabled: true }],
            genders: [{ code: 'M', enabled: true }],
            regions: [{ id: 1 }],
            seasons: [{ year: '2025', enabled: true }],
            time_ranges: [{ code: 'test-range', enabled: true }]
        };
        
        scraper.makeRequest = jest.fn().mockResolvedValue({
            data: testHelpers.createMockHTMLResponse()
        });
        
        scraper._parseRankingsTable = jest.fn().mockReturnValue([
            testHelpers.createMockSwimmerResult(),
            testHelpers.createMockSwimmerResult({ rank: 2, timeSeconds: 140.5 })
        ]);
    });
    
    afterEach(async () => {
        if (db) {
            await db.disconnect();
        }
    });
    
    test('should complete full scraping workflow', async () => {
        // Step 1: Initialize database
        await db.connect();
        expect(db.connect).toHaveBeenCalled();
        
        // Step 2: Load scraper configuration
        await scraper.loadConfig();
        expect(scraper.loadConfig).toHaveBeenCalled();
        
        // Step 3: Scrape all events
        const scrapingResults = await scraper.scrapeAllEvents();
        expect(scrapingResults).toBeDefined();
        expect(Object.keys(scrapingResults).length).toBeGreaterThan(0);
        
        // Step 4: Process and save results
        const processedResults = await processor.processScrapingResults(scrapingResults);
        expect(processedResults).toBeDefined();
        
        // Verify database operations were called
        expect(db.upsertEvent).toHaveBeenCalled();
        expect(db.upsertRegion).toHaveBeenCalled();
        expect(db.createManyRankings).toHaveBeenCalled();
        
        // Verify results structure
        const firstResult = Object.values(processedResults)[0];
        expect(firstResult.success).toBe(true);
        expect(firstResult.saved).toBeGreaterThan(0);
    });
    
    test('should handle scraping errors gracefully', async () => {
        // Mock scraping error
        scraper.scrapeAllEvents = jest.fn().mockRejectedValue(new Error('Network error'));
        
        await db.connect();
        await scraper.loadConfig();
        
        // Should not throw, but handle error gracefully
        await expect(scraper.scrapeAllEvents()).rejects.toThrow('Network error');
    });
    
    test('should handle database errors during processing', async () => {
        // Mock database error
        db.upsertEvent = jest.fn().mockRejectedValue(new Error('Database error'));
        
        await db.connect();
        await scraper.loadConfig();
        
        const scrapingResults = {
            'Test Event_2010_M_1': [testHelpers.createMockSwimmerResult()]
        };
        
        const processedResults = await processor.processScrapingResults(scrapingResults);
        
        // Should handle error gracefully
        expect(processedResults['Test Event_2010_M_1'].success).toBe(false);
        expect(processedResults['Test Event_2010_M_1'].error).toBe('Database error');
    });
    
    test('should process multiple events with different outcomes', async () => {
        const scrapingResults = {
            'Event1_2010_M_1': [
                testHelpers.createMockSwimmerResult(),
                testHelpers.createMockSwimmerResult({ rank: 2 })
            ],
            'Event2_2010_M_1': [], // Empty results
            'Event3_2010_M_1': [
                testHelpers.createMockSwimmerResult({ rank: 1 })
            ]
        };
        
        // Mock different outcomes for different events
        db.createManyRankings
            .mockResolvedValueOnce({ count: 2 }) // Event1: 2 rankings
            .mockResolvedValueOnce({ count: 0 }) // Event2: 0 rankings
            .mockResolvedValueOnce({ count: 1 }); // Event3: 1 ranking
        
        await db.connect();
        
        const processedResults = await processor.processScrapingResults(scrapingResults);
        
        expect(processedResults['Event1_2010_M_1'].saved).toBe(2);
        expect(processedResults['Event2_2010_M_1'].saved).toBe(0);
        expect(processedResults['Event3_2010_M_1'].saved).toBe(1);
        
        // All should be successful
        expect(processedResults['Event1_2010_M_1'].success).toBe(true);
        expect(processedResults['Event2_2010_M_1'].success).toBe(true);
        expect(processedResults['Event3_2010_M_1'].success).toBe(true);
    });
    
    test('should maintain data consistency during processing', async () => {
        const scrapingResults = {
            'Test Event_2010_M_1': [
                testHelpers.createMockSwimmerResult({
                    swimmerName: 'Test Swimmer',
                    rank: 1,
                    timeSeconds: 120.5
                })
            ]
        };
        
        await db.connect();
        
        await processor.processScrapingResults(scrapingResults);
        
        // Verify that old rankings are deleted before new ones are inserted
        expect(db.deleteRankingsForEvent).toHaveBeenCalledBefore(
            db.createManyRankings as jest.Mock
        );
        
        // Verify event and region are ensured before rankings are created
        expect(db.upsertEvent).toHaveBeenCalledBefore(
            db.createManyRankings as jest.Mock
        );
        expect(db.upsertRegion).toHaveBeenCalledBefore(
            db.createManyRankings as jest.Mock
        );
    });
    
    test('should handle concurrent processing correctly', async () => {
        const scrapingResults1 = {
            'Event1_2010_M_1': [testHelpers.createMockSwimmerResult()]
        };
        
        const scrapingResults2 = {
            'Event2_2010_M_1': [testHelpers.createMockSwimmerResult()]
        };
        
        await db.connect();
        
        // Process both concurrently
        const [results1, results2] = await Promise.all([
            processor.processScrapingResults(scrapingResults1),
            processor.processScrapingResults(scrapingResults2)
        ]);
        
        expect(results1['Event1_2010_M_1'].success).toBe(true);
        expect(results2['Event2_2010_M_1'].success).toBe(true);
    });
    
    test('should validate data integrity throughout workflow', async () => {
        const invalidRanking = {
            swimmerName: '', // Invalid: empty name
            rank: 0, // Invalid: zero rank
            timeSeconds: -1, // Invalid: negative time
            ageGroup: '2010',
            gender: 'M'
        };
        
        const scrapingResults = {
            'Test Event_2010_M_1': [invalidRanking]
        };
        
        await db.connect();
        
        // Mock validation in processor
        const originalValidate = processor.validateRankingData;
        processor.validateRankingData = jest.fn().mockReturnValue([
            'Schwimmername ist erforderlich',
            'Rang muss größer als 0 sein',
            'Zeit in Sekunden muss größer als 0 sein'
        ]);
        
        const processedResults = await processor.processScrapingResults(scrapingResults);
        
        // Should handle invalid data gracefully
        expect(processedResults).toBeDefined();
        
        // Restore original method
        processor.validateRankingData = originalValidate;
    });
    
    test('should cleanup resources properly', async () => {
        await db.connect();
        await scraper.loadConfig();
        
        const scrapingResults = await scraper.scrapeAllEvents();
        await processor.processScrapingResults(scrapingResults);
        
        // Cleanup
        await db.disconnect();
        
        expect(db.disconnect).toHaveBeenCalled();
    });
});

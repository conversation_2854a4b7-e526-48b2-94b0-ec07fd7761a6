<!DOCTYPE html>
<html>
    <head>
        <title>Der Objektverweis wurde nicht auf eine Objektinstanz festgelegt.</title>
        <meta name="viewport" content="width=device-width" />
        <style>
         body {font-family:"Verdana";font-weight:normal;font-size: .7em;color:black;} 
         p {font-family:"Verdana";font-weight:normal;color:black;margin-top: -5px}
         b {font-family:"Verdana";font-weight:bold;color:black;margin-top: -5px}
         H1 { font-family:"Verdana";font-weight:normal;font-size:18pt;color:red }
         H2 { font-family:"Verdana";font-weight:normal;font-size:14pt;color:maroon }
         pre {font-family:"Consolas","Lucida Console",Monospace;font-size:11pt;margin:0;padding:0.5em;line-height:14pt}
         .marker {font-weight: bold; color: black;text-decoration: none;}
         .version {color: gray;}
         .error {margin-bottom: 10px;}
         .expandable { text-decoration:underline; font-weight:bold; color:navy; cursor:pointer; }
         @media screen and (max-width: 639px) {
          pre { width: 440px; overflow: auto; white-space: pre-wrap; word-wrap: break-word; }
         }
         @media screen and (max-width: 479px) {
          pre { width: 280px; }
         }
        </style>
    </head>

    <body bgcolor="white">

            <span><H1>Serverfehler in der Anwendung /.<hr width=100% size=1 color=silver></H1>

            <h2> <i>Der Objektverweis wurde nicht auf eine Objektinstanz festgelegt.</i> </h2></span>

            <font face="Arial, Helvetica, Geneva, SunSans-Regular, sans-serif ">

            <b> Beschreibung: </b>Unbehandelte Ausnahme beim Ausführen der aktuellen Webanforderung. Überprüfen Sie die Stapelüberwachung, um weitere Informationen über diesen Fehler anzuzeigen und festzustellen, wo der Fehler im Code verursacht wurde.

            <br><br>

            <b> Ausnahmedetails: </b>System.NullReferenceException: Der Objektverweis wurde nicht auf eine Objektinstanz festgelegt.<br><br>

            <b>Quellfehler:</b> <br><br>

            <table width=100% bgcolor="#ffffcc">
               <tr>
                  <td>
                      <code>

Beim Ausf&#252;hren der aktuellen Webanforderung wurde einen unbehandelte Ausnahme generiert. Informationen &#252;ber den Ursprung und die Position der Ausnahme k&#246;nnen mit der Ausnahmestapel&#252;berwachung angezeigt werden.                      </code>

                  </td>
               </tr>
            </table>

            <br>

            <b>Stapelüberwachung:</b> <br><br>

            <table width=100% bgcolor="#ffffcc">
               <tr>
                  <td>
                      <code><pre>

[NullReferenceException: Der Objektverweis wurde nicht auf eine Objektinstanz festgelegt.]
   Website.Modules.Clubs.Index._rankingsButton_Click(Object sender, EventArgs e) in C:\Users\<USER>\Documents\Visual Studio 2022\Projects\SwimInfo\DSVPublic\Modules\Clubs\Index.aspx.cs:778
   System.Web.UI.WebControls.Button.OnClick(EventArgs e) +137
   System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument) +149
   System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint) +5444
</pre>                      </code>

                  </td>
               </tr>
            </table>

            <br>

            <hr width=100% size=1 color=silver>

            <b>Versionsinformationen:</b>&nbsp;Microsoft .NET Framework-Version:4.0.30319; ASP.NET-Version:4.8.4770.0

            </font>

    </body>
</html>
<!-- 
[NullReferenceException]: Der Objektverweis wurde nicht auf eine Objektinstanz festgelegt.
   bei Website.Modules.Clubs.Index._rankingsButton_Click(Object sender, EventArgs e) in C:\Users\<USER>\Documents\Visual Studio 2022\Projects\SwimInfo\DSVPublic\Modules\Clubs\Index.aspx.cs:Zeile 778.
   bei System.Web.UI.WebControls.Button.OnClick(EventArgs e)
   bei System.Web.UI.WebControls.Button.RaisePostBackEvent(String eventArgument)
   bei System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
[HttpUnhandledException]: Eine Ausnahme vom Typ &quot;System.Web.HttpUnhandledException&quot; wurde ausgel&#246;st.
   bei System.Web.UI.Page.HandleError(Exception e)
   bei System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   bei System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   bei System.Web.UI.Page.ProcessRequest()
   bei System.Web.UI.Page.ProcessRequest(HttpContext context)
   bei ASP.modules_clubs_index_aspx.ProcessRequest(HttpContext context)
   bei System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   bei System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   bei System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
--><!-- 
Diese Seite enthält möglicherweise vertrauliche Informationen, weil ASP.NET für die Anzeige ausführlicher Fehlermeldungen mit &lt;customErrors mode="Off"/&gt; konfiguriert ist. In Produktionsumgebungen sollten Sie &lt;customErrors mode="On"/&gt; oder &lt;customErrors mode="RemoteOnly"/&gt; verwenden.-->
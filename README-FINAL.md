# DSV Rankings - Node.js Version

Ein umfassendes System für das automatisierte Scraping und die Darstellung von DSV-Schwimmdaten, komplett in Node.js umgeschrieben.

## 🏊‍♂️ Features

- **Automatisiertes Scraping**: Wöchentliches Scraping von DSV-Daten mit Puppeteer
- **Express Web-Dashboard**: Moderne, responsive Web-Oberfläche
- **Prisma Database**: Type-safe Datenbankzugriff mit PostgreSQL/SQLite
- **Appwrite Integration**: Cloud-Deployment mit Appwrite Functions
- **Cron Scheduling**: Automatisierte Jobs mit node-cron
- **Comprehensive Testing**: Jest-basierte Test-Suite mit 70%+ Coverage
- **Email & Slack Notifications**: Benachrichtigungen bei Erfolg/Fehlern
- **CSV Export**: Datenexport für weitere Analysen

## 🚀 Schnellstart

### Voraussetzungen

- Node.js 18.0.0+
- npm 9.0.0+
- PostgreSQL (optional, SQLite als Alternative)

### Lokale Installation

```bash
# Repository klonen
git clone <repository-url>
cd dsv-scraper

# Dependencies installieren
npm install

# Environment-Datei erstellen
cp .env.nodejs.example .env
# Bearbeite .env mit deinen Konfigurationen

# Datenbank initialisieren
npm run db:generate
npm run db:push

# Server starten
npm run dev  # Development
npm start    # Production

# Scheduler starten (optional)
npm run scheduler
```

## 📁 Projektstruktur

```
dsv-scraper/
├── src/
│   ├── config.js              # Zentrale Konfiguration
│   ├── index.js               # Web-App Entry Point
│   ├── scheduler.js           # Scheduler Entry Point
│   ├── web/                   # Express Web Application
│   │   ├── app.js
│   │   ├── routes/
│   │   │   ├── api.js         # API Routes
│   │   │   └── web.js         # Web Routes
│   │   ├── views/             # EJS Templates
│   │   └── public/            # Statische Dateien
│   ├── scrapers/              # Scraping-Module
│   │   ├── BaseScraper.js
│   │   ├── DSVScraper.js
│   │   └── config/
│   │       └── events.json
│   ├── utils/                 # Hilfsfunktionen
│   │   ├── DatabaseManager.js
│   │   ├── AppwriteDatabase.js
│   │   ├── DataProcessor.js
│   │   └── logger.js
│   └── scheduler/             # Scheduling & Notifications
│       ├── CronJobs.js
│       └── Notifications.js
├── prisma/
│   └── schema.prisma          # Datenbankschema
├── tests/                     # Umfassende Test-Suite
│   ├── setup.js
│   ├── config.test.js
│   ├── utils/
│   ├── scrapers/
│   ├── scheduler/
│   ├── web/
│   └── integration/
├── scripts/
│   ├── test.sh               # Test-Ausführung
│   ├── cleanup.sh
│   └── deploy-appwrite.sh
├── data/                     # Datenverzeichnisse
├── package.json
├── jest.config.js
├── TESTING.md               # Test-Dokumentation
└── README-NODEJS.md         # Detaillierte Dokumentation
```

## 🧪 Testing

### Test-Suite ausführen

```bash
# Alle Tests
npm test

# Tests mit Coverage
npm test -- --coverage

# Spezifische Tests
npm test tests/config.test.js

# Watch Mode
npm run test:watch

# Vollständige Test-Suite mit Script
./scripts/test.sh
```

### Test-Kategorien

- **Unit Tests**: Einzelne Module (Config, Logger, Database, Scraper)
- **Integration Tests**: Vollständiger Scraping-Workflow
- **API Tests**: REST-Endpoints mit Supertest
- **Performance Tests**: Timing und Memory-Tests

### Coverage-Ziele

- Lines: 70%+
- Functions: 70%+
- Branches: 70%+
- Statements: 70%+

## 🗄️ Datenbank

### Prisma Setup

```bash
# Schema generieren
npm run db:generate

# Datenbank migrieren
npm run db:migrate

# Datenbank Studio öffnen
npm run db:studio
```

### Unterstützte Datenbanken

- **PostgreSQL** (empfohlen für Production)
- **SQLite** (für Development)
- **Appwrite Database** (für Cloud-Deployment)

## 🌐 API Endpoints

### Web Interface
- `GET /` - Dashboard
- `GET /rankings` - Rankings-Übersicht
- `GET /events` - Events-Verwaltung
- `GET /regions` - Regionen-Übersicht
- `GET /logs` - Scraping-Logs
- `GET /statistics` - Statistiken

### REST API
- `GET /api/rankings` - Rankings abrufen
- `GET /api/rankings/top` - Top Rankings
- `GET /api/events` - Events abrufen
- `GET /api/regions` - Regionen abrufen
- `GET /api/statistics` - Statistiken
- `GET /api/scrape-logs` - Scraping-Logs
- `GET /api/export/csv` - CSV Export

### Health Check
- `GET /health` - System-Gesundheitsprüfung

## ⏰ Scheduler

### Automatische Jobs

- **Wöchentliches Scraping**: Sonntags 06:00 UTC
- **Tägliche Bereinigung**: Täglich 02:00 UTC
- **Stündliche Gesundheitsprüfung**: Jede volle Stunde

### Manuelle Ausführung

```bash
# Scheduler starten
npm run scheduler

# Einzelne Jobs (via API)
curl "http://localhost:3000/scheduler?action=weekly_scrape"
curl "http://localhost:3000/scheduler?action=health_check"
curl "http://localhost:3000/scheduler?action=cleanup"
```

## 🚀 Deployment

### Appwrite Cloud

```bash
# Appwrite-Konfiguration
cp .env.appwrite.nodejs.example .env.appwrite
# Bearbeite .env.appwrite mit Appwrite-Credentials

# Deployment
npm run deploy:appwrite
```

### Docker (falls gewünscht)

```bash
# Dockerfile erstellen für Node.js
# docker build -t dsv-scraper-nodejs .
# docker run -p 3000:3000 dsv-scraper-nodejs
```

## 🔄 Migration von Python

### Hauptunterschiede

| Python Version | Node.js Version |
|----------------|-----------------|
| Flask | Express.js |
| SQLAlchemy | Prisma |
| BeautifulSoup + Requests | Cheerio + Axios + Puppeteer |
| APScheduler | node-cron |
| pytest | Jest |

### Daten-Migration

Die Datenbankstruktur ist kompatibel. Bestehende PostgreSQL-Daten können weiterverwendet werden.

## 📚 Dokumentation

- `README-NODEJS.md` - Detaillierte Node.js Dokumentation
- `TESTING.md` - Umfassende Test-Anleitung
- `DEPLOYMENT-APPWRITE.md` - Appwrite Deployment Guide

## 🤝 Contributing

1. Fork das Repository
2. Erstelle einen Feature-Branch
3. Führe Tests aus: `npm test`
4. Committe deine Änderungen
5. Push zum Branch
6. Erstelle einen Pull Request

## 📝 License

MIT License - siehe LICENSE-Datei für Details.

---

**✅ Migration abgeschlossen**: Das DSV-Scraper Projekt wurde erfolgreich von Python auf Node.js umgeschrieben mit umfassender Test-Suite und moderner Architektur.

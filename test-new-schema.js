#!/usr/bin/env node

/**
 * Test für das neue Datenbank-Schema
 * Testet ob verschiedene Altersgruppen für dasselbe Event gespeichert werden können
 */

import { DSVScraper } from './src/scrapers/DSVScraper.js';
import { DataProcessor } from './src/utils/DataProcessor.js';
import DatabaseManager from './src/utils/DatabaseManager.js';

async function testNewSchema() {
    console.log('🧪 Test: Neues Datenbank-Schema');
    console.log('==============================');
    
    let scraper, dataProcessor, dbManager;
    
    try {
        // Komponenten initialisieren
        console.log('\n📦 Initialisiere Komponenten...');
        
        dbManager = new DatabaseManager();
        await dbManager.connect();
        console.log('  ✅ Datenbank verbunden');
        
        dataProcessor = new DataProcessor(dbManager);
        console.log('  ✅ Data Processor bereit');
        
        scraper = new DSVScraper('scrapers/config/events.json');
        await scraper.loadConfig();
        console.log('  ✅ DSV Scraper initialisiert');
        
        // Test 1: 50m Schmetterling, männlich, alle Altersgruppen, Baden
        console.log('\n📋 Test 1: 50m Schmetterling, M, alle Altersgruppen, Baden');
        const config1 = {
            eventName: '50m Schmetterling',
            eventCode: '50S|GL',
            distance: 50,
            stroke: 'Schmetterling',
            course: 'L',
            gender: 'M',
            season: '2025',
            regionId: 1,
            ageGroup: '-1|-1'  // Alle Altersgruppen
        };
        
        const results1 = await scraper.scrapeEvent(config1);
        console.log(`✅ Scraping: ${results1.length} Rankings gefunden`);
        
        if (results1.length > 0) {
            // Daten in Datenbank speichern
            const eventKey1 = '50S|GL_M_ALL_2025_1_L';
            await dataProcessor.processEventResults(eventKey1, results1.map(result => ({
                ...result,
                eventName: config1.eventName,
                eventCode: config1.eventCode,
                distance: config1.distance,
                stroke: config1.stroke,
                gender: config1.gender,
                season: config1.season,
                ageGroup: config1.ageGroup,
                regionId: config1.regionId,
                scrapedAt: new Date()
            })));
            console.log(`✅ Datenbank: ${results1.length} Rankings gespeichert`);
        }
        
        // Test 2: 50m Schmetterling, männlich, Jahrgang 2015, Baden
        console.log('\n📋 Test 2: 50m Schmetterling, M, Jahrgang 2015, Baden');
        const config2 = {
            eventName: '50m Schmetterling',
            eventCode: '50S|GL',
            distance: 50,
            stroke: 'Schmetterling',
            course: 'L',
            gender: 'M',
            season: '2025',
            regionId: 1,
            ageGroup: '2015|2015'  // Jahrgang 2015
        };
        
        const results2 = await scraper.scrapeEvent(config2);
        console.log(`✅ Scraping: ${results2.length} Rankings gefunden`);
        
        if (results2.length > 0) {
            // Daten in Datenbank speichern
            const eventKey2 = '50S|GL_M_2015|2015_2025_1_L';
            await dataProcessor.processEventResults(eventKey2, results2.map(result => ({
                ...result,
                eventName: config2.eventName,
                eventCode: config2.eventCode,
                distance: config2.distance,
                stroke: config2.stroke,
                gender: config2.gender,
                season: config2.season,
                ageGroup: config2.ageGroup,
                regionId: config2.regionId,
                scrapedAt: new Date()
            })));
            console.log(`✅ Datenbank: ${results2.length} Rankings gespeichert`);
        }
        
        // Test 3: Prüfe ob beide Events in der Datenbank existieren
        console.log('\n📊 Test 3: Prüfe Events in Datenbank');
        
        const allEvents = await dbManager.prisma.event.findMany({
            where: {
                code: '50S|GL',
                gender: 'M'
            },
            orderBy: {
                ageGroup: 'asc'
            }
        });
        
        console.log(`✅ Events gefunden: ${allEvents.length}`);
        allEvents.forEach((event, i) => {
            console.log(`  ${i+1}. ${event.name} - ${event.gender} - AgeGroup: ${event.ageGroup}`);
            console.log(`     ID: ${event.id}, Code: ${event.code}`);
        });
        
        // Test 4: Prüfe Rankings für beide Events
        console.log('\n📈 Test 4: Prüfe Rankings');
        
        for (const event of allEvents) {
            const rankings = await dbManager.prisma.ranking.findMany({
                where: {
                    eventId: event.id
                },
                take: 3,
                orderBy: {
                    rank: 'asc'
                }
            });
            
            console.log(`\n🏆 ${event.name} (${event.ageGroup === '-1|-1' ? 'Alle Altersgruppen' : 'Jahrgang 2015'}):`);
            console.log(`   ${rankings.length} Rankings total`);
            rankings.forEach((ranking, i) => {
                console.log(`   ${i+1}. ${ranking.swimmerName} - ${ranking.time} (${ranking.club})`);
            });
        }
        
        console.log('\n🎉 Schema-Test erfolgreich abgeschlossen!');
        console.log('✅ Verschiedene Altersgruppen können für dasselbe Event gespeichert werden');
        
    } catch (error) {
        console.error('\n❌ Fehler beim Schema-Test:', error.message);
        console.error('Stack:', error.stack);
    } finally {
        // Cleanup
        if (dbManager) {
            await dbManager.disconnect();
            console.log('\n📦 Datenbankverbindung geschlossen');
        }
    }
}

// Test starten
testNewSchema().catch(console.error);

#!/usr/bin/env node

/**
 * Spezifisches Scraping für 50m Schmetterling, männlich
 * Alle Regionen und beide Kurse (Langbahn und Kurzbahn)
 */

import { DSVScraper } from './src/scrapers/DSVScraper.js';
import { DataProcessor } from './src/utils/DataProcessor.js';
import DatabaseManager from './src/utils/DatabaseManager.js';

async function scrape50SchwimmMale() {
    console.log('🏊‍♂️ Scraping: 50m Schmetterling, männlich');
    console.log('==========================================');
    
    let scraper, dataProcessor, dbManager;
    
    try {
        // Komponenten initialisieren
        console.log('\n📦 Initialisiere Komponenten...');
        
        dbManager = new DatabaseManager();
        await dbManager.connect();
        console.log('  ✅ Datenbank verbunden');
        
        dataProcessor = new DataProcessor();
        console.log('  ✅ Data Processor bereit');
        
        scraper = new DSVScraper('scrapers/config/events.json');
        await scraper.loadConfig();
        console.log('  ✅ DSV Scraper initialisiert');
        
        // Event-Konfiguration
        const eventConfig = {
            eventName: '50m Schmetterling',
            eventCode: '50S|GL',
            distance: 50,
            stroke: 'Schmetterling',
            gender: 'M',
            season: '2025',
            ageGroup: '2015|2015'  // Jahrgang 2015 (10 Jahre)
        };

        console.log('\n📋 Event-Konfiguration:');
        console.log(`  Event: ${eventConfig.eventName} (${eventConfig.eventCode})`);
        console.log(`  Gender: ${eventConfig.gender} (männlich)`);
        console.log(`  Jahrgang: 2015 (10 Jahre)`);
        console.log(`  Saison: ${eventConfig.season}`);
        
        // Alle Regionen laden
        const regions = scraper.config.regions;
        console.log(`\n🌍 Scraping für ${regions.length} Regionen:`);
        regions.forEach(region => {
            console.log(`  - ${region.name} (ID: ${region.id})`);
        });
        
        // Beide Kurse scrapen
        const courses = [
            { code: 'L', name: 'Langbahn (50m)' },
            { code: 'S', name: 'Kurzbahn (25m)' }
        ];
        
        let totalResults = 0;
        const allResults = [];
        
        for (const course of courses) {
            console.log(`\n🏊 Scraping ${course.name}...`);
            console.log('='.repeat(40));
            
            for (const region of regions) {
                const config = {
                    ...eventConfig,
                    course: course.code,
                    regionId: region.id
                };
                
                console.log(`\n📍 Region: ${region.name} (${course.name})`);
                
                try {
                    const results = await scraper.scrapeEvent(config);
                    
                    if (results.length > 0) {
                        console.log(`  ✅ ${results.length} Rankings gefunden`);
                        
                        // Erste 3 Ergebnisse anzeigen
                        console.log('  Top 3:');
                        results.slice(0, 3).forEach((result, i) => {
                            console.log(`    ${i+1}. ${result.swimmerName} - ${result.time} (${result.club})`);
                        });
                        
                        // Ergebnisse zur Gesamtliste hinzufügen
                        results.forEach(result => {
                            allResults.push({
                                ...result,
                                region: region.name,
                                course: course.name,
                                courseCode: course.code
                            });
                        });
                        
                        totalResults += results.length;
                    } else {
                        console.log('  ⚠️  Keine Rankings gefunden');
                    }
                    
                    // Kurze Pause zwischen Anfragen
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                } catch (error) {
                    console.log(`  ❌ Fehler: ${error.message}`);
                }
            }
        }
        
        console.log('\n📊 ZUSAMMENFASSUNG');
        console.log('==================');
        console.log(`Gesamt gefundene Rankings: ${totalResults}`);
        console.log(`Eindeutige Schwimmer: ${new Set(allResults.map(r => r.swimmerName)).size}`);
        
        // Beste Zeiten pro Kurs
        const langbahnResults = allResults.filter(r => r.courseCode === 'L');
        const kurbbahnResults = allResults.filter(r => r.courseCode === 'S');
        
        if (langbahnResults.length > 0) {
            const bestLangbahn = langbahnResults.sort((a, b) => a.time.localeCompare(b.time))[0];
            console.log(`\n🥇 Beste Zeit Langbahn: ${bestLangbahn.swimmerName} - ${bestLangbahn.time} (${bestLangbahn.club}, ${bestLangbahn.region})`);
        }
        
        if (kurbbahnResults.length > 0) {
            const bestKurzbahn = kurbbahnResults.sort((a, b) => a.time.localeCompare(b.time))[0];
            console.log(`🥇 Beste Zeit Kurzbahn: ${bestKurzbahn.swimmerName} - ${bestKurzbahn.time} (${bestKurzbahn.club}, ${bestKurzbahn.region})`);
        }
        
        // Regionen mit den meisten Rankings
        const regionStats = {};
        allResults.forEach(result => {
            if (!regionStats[result.region]) {
                regionStats[result.region] = 0;
            }
            regionStats[result.region]++;
        });
        
        console.log('\n📈 Rankings pro Region:');
        Object.entries(regionStats)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .forEach(([region, count]) => {
                console.log(`  ${region}: ${count} Rankings`);
            });
        
        // Daten in Datenbank speichern
        if (totalResults > 0) {
            console.log('\n💾 Speichere Daten in Datenbank...');
            try {
                // Daten für die Datenbank vorbereiten
                const processedResults = allResults.map(result => ({
                    ...result,
                    eventName: eventConfig.eventName,
                    eventCode: eventConfig.eventCode,
                    distance: eventConfig.distance,
                    stroke: eventConfig.stroke,
                    gender: eventConfig.gender,
                    ageGroup: eventConfig.ageGroup,
                    season: eventConfig.season,
                    scrapedAt: new Date()
                }));

                await dataProcessor.processResults(processedResults);
                console.log(`  ✅ ${processedResults.length} Datensätze erfolgreich gespeichert`);
            } catch (error) {
                console.log(`  ⚠️  Fehler beim Speichern: ${error.message}`);
                console.log(`  Stack: ${error.stack}`);
            }
        }
        
        console.log('\n🎉 Scraping abgeschlossen!');
        
    } catch (error) {
        console.error('\n❌ Fehler beim Scraping:', error.message);
        console.error('Stack:', error.stack);
    } finally {
        // Cleanup
        if (dbManager) {
            await dbManager.disconnect();
            console.log('📦 Datenbankverbindung geschlossen');
        }
    }
}

// Scraping starten
scrape50SchwimmMale().catch(console.error);

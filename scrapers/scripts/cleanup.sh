#!/bin/bash

# DSV Scraper Cleanup Script
# Entfernt lokale Entwicklungsdateien für Cloud-Deployment

set -e

echo "🧹 DSV Scraper Cleanup für Cloud-Deployment"
echo "============================================"

# Farben für Output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Funktionen
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Bestätigung vom Benutzer
confirm_cleanup() {
    echo ""
    log_warn "Folgende Dateien/Ordner werden gelöscht:"
    echo "  • data/logs/* (lokale Log-Dateien)"
    echo "  • data/raw/* (temporäre Scraping-Daten)"
    echo "  • data/processed/* (lokale Exports)"
    echo "  • __pycache__ (Python Cache-Dateien)"
    echo ""
    
    read -p "Möchten Sie fortfahren? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Cleanup abgebrochen."
        exit 0
    fi
}

# CSV-Exports entfernen
cleanup_csv() {
    log_info "Entferne CSV-Dateien..."

    # Entferne CSV-Dateien in data/processed/
    if [ -d "data/processed" ]; then
        find data/processed -name "*.csv" -type f -delete 2>/dev/null || true
        log_info "✓ CSV-Dateien in data/processed/ entfernt"
    fi
}

# Log-Dateien entfernen
cleanup_logs() {
    log_info "Entferne lokale Log-Dateien..."
    
    if [ -d "data/logs" ]; then
        rm -f data/logs/*.log 2>/dev/null || true
        log_info "✓ Log-Dateien entfernt"
    fi
    
    if [ -d "logs" ]; then
        rm -f logs/*.log 2>/dev/null || true
        log_info "✓ Log-Dateien entfernt"
    fi
}

# Temporäre Daten entfernen
cleanup_temp_data() {
    log_info "Entferne temporäre Daten..."
    
    if [ -d "data/raw" ]; then
        find data/raw -type f -not -name ".gitkeep" -delete 2>/dev/null || true
        log_info "✓ Temporäre Raw-Daten entfernt"
    fi
    
    if [ -d "data/archive" ]; then
        find data/archive -type f -not -name ".gitkeep" -delete 2>/dev/null || true
        log_info "✓ Archiv-Daten entfernt"
    fi
}

# Python Cache entfernen
cleanup_python_cache() {
    log_info "Entferne Python Cache-Dateien..."
    
    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
    find . -name "*.pyc" -delete 2>/dev/null || true
    find . -name "*.pyo" -delete 2>/dev/null || true
    
    log_info "✓ Python Cache entfernt"
}

# .gitkeep Dateien erstellen
create_gitkeep() {
    log_info "Erstelle .gitkeep Dateien..."
    
    directories=("data/raw" "data/processed" "data/archive" "data/logs")
    
    for dir in "${directories[@]}"; do
        if [ -d "$dir" ]; then
            touch "$dir/.gitkeep"
            log_info "✓ $dir/.gitkeep erstellt"
        fi
    done
}

# Backup erstellen (optional) - für zukünftige lokale Entwicklung
create_backup() {
    # Prüfe auf lokale SQLite-Datenbanken
    local db_files=("dsv_rankings_dev.db" "dsv_rankings.db" "instance/dsv_rankings.db")
    local found_db=false

    for db_file in "${db_files[@]}"; do
        if [ -f "$db_file" ]; then
            log_info "Erstelle Backup von $db_file..."
            backup_name="backup_$(basename "$db_file" .db)_$(date +%Y%m%d_%H%M%S).db"
            cp "$db_file" "$backup_name"
            log_info "✓ Backup erstellt: $backup_name"
            found_db=true
        fi
    done

    if [ "$found_db" = true ]; then
        log_warn "Backup kann nach erfolgreichem Cloud-Deployment gelöscht werden"
    else
        log_info "Keine lokalen Datenbanken für Backup gefunden"
    fi
}

# Hauptfunktion
main() {
    echo ""
    log_info "Starte Cleanup-Prozess..."
    
    # Prüfe ob wir im richtigen Verzeichnis sind
    if [ ! -f "manage.py" ] || [ ! -f "requirements.txt" ]; then
        log_error "Nicht im DSV Scraper Hauptverzeichnis!"
        exit 1
    fi
    
    # Bestätigung
    confirm_cleanup
    
    echo ""
    log_info "Führe Cleanup durch..."
    
    # Optional: Backup erstellen
    read -p "Backup der lokalen Datenbank erstellen? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        create_backup
    fi
    
    # Cleanup-Funktionen ausführen
    cleanup_csv
    cleanup_logs
    cleanup_temp_data
    cleanup_python_cache
    create_gitkeep
    
    echo ""
    log_info "🎉 Cleanup abgeschlossen!"
    echo ""
    log_info "Das System ist jetzt bereit für Appwrite-Deployment:"
    echo "  ✓ CSV-Exports bereinigt"
    echo "  ✓ Log-Dateien bereinigt"
    echo "  ✓ Temporäre Dateien bereinigt"
    echo "  ✓ Python Cache entfernt"
    echo "  ✓ .gitkeep Dateien erstellt"
    echo ""
    log_info "Nächste Schritte:"
    echo "  1. git add . && git commit -m 'Cleanup für Appwrite-Deployment'"
    echo "  2. Appwrite CLI konfigurieren"
    echo "  3. Environment Variables setzen"
    echo "  4. Appwrite-Deployment starten"
}

# Skript ausführen
main "$@"

#!/usr/bin/env node

/**
 * Initial Scraping Script for DSV Rankings
 * Simple script to get started with scraping
 */

import { DSVScraper } from '../src/scrapers/DSVScraper.js';
import { DataProcessor } from '../src/utils/DataProcessor.js';
import DatabaseManager from '../src/utils/DatabaseManager.js';

async function initialScrape() {
    console.log('🚀 DSV Initial Scraping');
    console.log('=======================');
    console.log('Starting initial data collection from DSV system...\n');
    
    const startTime = Date.now();
    let db, processor, scraper;
    
    try {
        // Step 1: Initialize components
        console.log('📦 Step 1: Initializing components...');
        
        db = new DatabaseManager();
        await db.connect();
        console.log('  ✅ Database connected');
        
        processor = new DataProcessor(db);
        console.log('  ✅ Data processor ready');
        
        scraper = new DSVScraper('src/scrapers/config/events.json');
        console.log('  ✅ DSV scraper initialized');
        
        // Step 2: Load configuration
        console.log('\n📋 Step 2: Loading scraping configuration...');
        await scraper.loadConfig();
        
        const config = scraper.config;
        console.log(`  ✅ Configuration loaded:`);
        console.log(`     - Events: ${config.events?.length || 0}`);
        console.log(`     - Age Groups: ${config.age_groups?.length || 0}`);
        console.log(`     - Genders: ${config.genders?.length || 0}`);
        console.log(`     - Regions: ${config.regions?.length || 0}`);
        
        if (!config.events || config.events.length === 0) {
            console.log('\n⚠️  No events configured! Please add events to src/scrapers/config/events.json');
            console.log('Example configuration:');
            console.log(JSON.stringify({
                events: [
                    {
                        name: "200m Lagen",
                        code: "200L|GL",
                        distance: 200,
                        stroke: "Lagen",
                        course: "L"
                    }
                ],
                age_groups: [
                    { year: "2010|2010", name: "15 Jahre - JG 2010" }
                ],
                genders: [
                    { code: "M", name: "Männlich" },
                    { code: "W", name: "Weiblich" }
                ],
                regions: [
                    { id: 1, name: "Schleswig-Holstein" }
                ]
            }, null, 2));
            return;
        }
        
        // Step 3: Start scraping
        console.log('\n🔍 Step 3: Starting scraping process...');
        console.log('This may take several minutes depending on the number of events...\n');
        
        const results = await scraper.scrapeAllEvents();
        const eventCount = Object.keys(results).length;
        
        console.log(`\n📈 Step 4: Scraping completed!`);
        console.log(`  ✅ ${eventCount} event combinations processed`);
        
        // Show raw results summary
        const totalRankings = Object.values(results).reduce((sum, rankings) => sum + rankings.length, 0);
        console.log(`  📊 ${totalRankings} total rankings found`);
        
        if (totalRankings === 0) {
            console.log('\n⚠️  No rankings found. This could mean:');
            console.log('   - The events are not available in the current season');
            console.log('   - The age groups have no data');
            console.log('   - The DSV website structure has changed');
            console.log('   - Network connectivity issues');
            return;
        }
        
        // Step 5: Process and save results
        console.log('\n💾 Step 5: Processing and saving results...');
        const processed = await processor.processScrapingResults(results);
        
        // Generate summary
        const summary = {
            successful: 0,
            failed: 0,
            totalSaved: 0,
            events: {}
        };
        
        Object.entries(processed).forEach(([key, result]) => {
            if (result.success) {
                summary.successful++;
                summary.totalSaved += result.saved || 0;
            } else {
                summary.failed++;
            }
            
            const [eventName] = key.split('_');
            if (!summary.events[eventName]) {
                summary.events[eventName] = { saved: 0, failed: 0 };
            }
            
            if (result.success) {
                summary.events[eventName].saved += result.saved || 0;
            } else {
                summary.events[eventName].failed++;
            }
        });
        
        // Step 6: Show final results
        console.log('\n🎉 Step 6: Initial scraping completed!');
        console.log('=====================================');
        console.log(`✅ Successful events: ${summary.successful}`);
        console.log(`❌ Failed events: ${summary.failed}`);
        console.log(`📊 Total rankings saved: ${summary.totalSaved}`);
        console.log(`⏱️  Total duration: ${Math.round((Date.now() - startTime) / 1000)}s`);
        
        if (summary.totalSaved > 0) {
            console.log('\n📋 Results by Event:');
            Object.entries(summary.events).forEach(([eventName, stats]) => {
                if (stats.saved > 0) {
                    console.log(`  ${eventName}: ${stats.saved} rankings`);
                }
            });
            
            console.log('\n🌐 Next Steps:');
            console.log('  1. Visit the dashboard: http://localhost:3000/');
            console.log('  2. View rankings: http://localhost:3000/rankings');
            console.log('  3. Export data: http://localhost:3000/api/export/csv');
            console.log('  4. Check statistics: http://localhost:3000/statistics');
        }
        
        if (summary.failed > 0) {
            console.log('\n⚠️  Some events failed to scrape. Check the logs for details.');
        }
        
    } catch (error) {
        console.error('\n❌ Initial scraping failed:', error.message);
        console.error('Stack trace:', error.stack);
        process.exit(1);
        
    } finally {
        // Cleanup
        if (db) {
            await db.disconnect();
            console.log('\n🔌 Database disconnected');
        }
    }
}

// Handle process signals
process.on('SIGINT', () => {
    console.log('\n\n⏹️  Scraping interrupted by user');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n\n⏹️  Scraping terminated');
    process.exit(0);
});

// Run the initial scraping
console.log('Starting DSV Initial Scraping...\n');
initialScrape().catch(error => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
});

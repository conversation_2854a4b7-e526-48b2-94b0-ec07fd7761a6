#!/usr/bin/env node

/**
 * Manual Scraping Script for DSV Rankings
 * Usage: node scripts/manual-scrape.js [options]
 */

import { DSVScraper } from '../src/scrapers/DSVScraper.js';
import { DataProcessor } from '../src/utils/DataProcessor.js';
import DatabaseManager from '../src/utils/DatabaseManager.js';
import { logScrapingStart, logScrapingSuccess, logScrapingError } from '../src/utils/logger.js';

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
    eventName: args.find(arg => arg.startsWith('--event='))?.split('=')[1],
    ageGroup: args.find(arg => arg.startsWith('--age='))?.split('=')[1],
    gender: args.find(arg => arg.startsWith('--gender='))?.split('=')[1],
    regionId: args.find(arg => arg.startsWith('--region='))?.split('=')[1],
    dryRun: args.includes('--dry-run'),
    verbose: args.includes('--verbose') || args.includes('-v')
};

async function manualScrape() {
    console.log('🚀 DSV Manual Scraping Tool');
    console.log('============================');
    
    if (options.dryRun) {
        console.log('🔍 DRY RUN MODE - No data will be saved');
    }
    
    const startTime = Date.now();
    let db, processor, scraper;
    
    try {
        // Initialize components
        console.log('📦 Initializing components...');
        
        db = new DatabaseManager();
        await db.connect();
        console.log('✅ Database connected');
        
        processor = new DataProcessor(db);
        scraper = new DSVScraper('src/scrapers/config/events.json');
        
        // Load configuration
        console.log('📋 Loading scraping configuration...');
        await scraper.loadConfig();
        console.log('✅ Configuration loaded');
        
        if (options.verbose) {
            console.log('📊 Configuration summary:');
            console.log(`  - Events: ${scraper.config.events?.length || 0}`);
            console.log(`  - Age Groups: ${scraper.config.age_groups?.length || 0}`);
            console.log(`  - Genders: ${scraper.config.genders?.length || 0}`);
            console.log(`  - Regions: ${scraper.config.regions?.length || 0}`);
        }
        
        // Filter configuration based on options
        if (options.eventName || options.ageGroup || options.gender || options.regionId) {
            console.log('🔍 Applying filters...');
            scraper.config = filterConfiguration(scraper.config, options);
            
            if (options.verbose) {
                console.log('📊 Filtered configuration:');
                console.log(`  - Events: ${scraper.config.events?.length || 0}`);
                console.log(`  - Age Groups: ${scraper.config.age_groups?.length || 0}`);
                console.log(`  - Genders: ${scraper.config.genders?.length || 0}`);
                console.log(`  - Regions: ${scraper.config.regions?.length || 0}`);
            }
        }
        
        // Start scraping
        console.log('🔍 Starting scraping process...');
        logScrapingStart('Manual Scraping', options);
        
        const results = await scraper.scrapeAllEvents();
        const eventCount = Object.keys(results).length;
        
        console.log(`📈 Scraping completed: ${eventCount} event combinations processed`);
        
        if (options.verbose) {
            console.log('📊 Raw results summary:');
            Object.entries(results).forEach(([key, rankings]) => {
                console.log(`  - ${key}: ${rankings.length} rankings`);
            });
        }
        
        if (!options.dryRun) {
            // Process and save results
            console.log('💾 Processing and saving results...');
            const processed = await processor.processScrapingResults(results);
            
            // Generate summary
            const summary = generateSummary(processed);
            
            console.log('📈 Processing Summary:');
            console.log('=====================');
            console.log(`✅ Successful events: ${summary.successful}`);
            console.log(`❌ Failed events: ${summary.failed}`);
            console.log(`📊 Total rankings saved: ${summary.totalSaved}`);
            console.log(`⏱️  Total duration: ${Math.round((Date.now() - startTime) / 1000)}s`);
            
            if (options.verbose) {
                console.log('\n📋 Detailed Results:');
                console.table(
                    Object.entries(processed).map(([key, result]) => ({
                        Event: key.split('_')[0],
                        AgeGroup: key.split('_')[1],
                        Gender: key.split('_')[2],
                        Region: key.split('_')[3],
                        Success: result.success ? '✅' : '❌',
                        Saved: result.saved || 0,
                        Error: result.error ? result.error.substring(0, 50) + '...' : '-'
                    }))
                );
            }
            
            logScrapingSuccess('Manual Scraping', summary.totalSaved, Date.now() - startTime);
        } else {
            console.log('🔍 DRY RUN - Results not saved to database');
            const totalRankings = Object.values(results).reduce((sum, rankings) => sum + rankings.length, 0);
            console.log(`📊 Would have saved ${totalRankings} rankings`);
        }
        
    } catch (error) {
        console.error('❌ Scraping failed:', error.message);
        if (options.verbose) {
            console.error('Stack trace:', error.stack);
        }
        logScrapingError('Manual Scraping', error, options);
        process.exit(1);
        
    } finally {
        // Cleanup
        if (db) {
            await db.disconnect();
            console.log('🔌 Database disconnected');
        }
    }
    
    console.log('🎉 Manual scraping completed successfully!');
}

function filterConfiguration(config, options) {
    const filtered = { ...config };
    
    if (options.eventName) {
        filtered.events = config.events?.filter(e => 
            e.name.toLowerCase().includes(options.eventName.toLowerCase())
        );
    }
    
    if (options.ageGroup) {
        filtered.age_groups = config.age_groups?.filter(ag => 
            ag.year === options.ageGroup
        );
    }
    
    if (options.gender) {
        filtered.genders = config.genders?.filter(g => 
            g.code.toLowerCase() === options.gender.toLowerCase()
        );
    }
    
    if (options.regionId) {
        filtered.regions = config.regions?.filter(r => 
            r.id === parseInt(options.regionId)
        );
    }
    
    return filtered;
}

function generateSummary(processed) {
    const summary = {
        successful: 0,
        failed: 0,
        totalSaved: 0
    };
    
    Object.values(processed).forEach(result => {
        if (result.success) {
            summary.successful++;
            summary.totalSaved += result.saved || 0;
        } else {
            summary.failed++;
        }
    });
    
    return summary;
}

function showHelp() {
    console.log(`
DSV Manual Scraping Tool

Usage: node scripts/manual-scrape.js [options]

Options:
  --event=NAME      Filter by event name (partial match)
  --age=YEAR        Filter by age group (e.g., 2010)
  --gender=M|W      Filter by gender (M or W)
  --region=ID       Filter by region ID (e.g., 1)
  --dry-run         Don't save results to database
  --verbose, -v     Show detailed output
  --help, -h        Show this help

Examples:
  node scripts/manual-scrape.js
  node scripts/manual-scrape.js --event="200m Lagen" --age=2010
  node scripts/manual-scrape.js --gender=M --region=1 --verbose
  node scripts/manual-scrape.js --dry-run --verbose
`);
}

// Handle help
if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    process.exit(0);
}

// Run the scraping
manualScrape().catch(error => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
});

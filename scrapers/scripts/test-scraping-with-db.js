#!/usr/bin/env node

/**
 * Test Scraping with Database
 * Tests the complete scraping pipeline including database storage
 */

import { DSVScraper } from '../src/scrapers/DSVScraper.js';
import { DataProcessor } from '../src/utils/DataProcessor.js';
import DatabaseManager from '../src/utils/DatabaseManager.js';
import { ScrapingConfig } from '../src/scrapers/BaseScraper.js';
import { scraperLogger } from '../src/utils/logger.js';

async function testScrapingWithDatabase() {
    console.log('🧪 DSV Scraping + Database Test');
    console.log('================================');
    console.log('Testing the complete scraping pipeline with database storage...\n');
    
    let db, dataProcessor, scraper;
    
    try {
        // Step 1: Initialize components
        console.log('📦 Step 1: Initializing components...');
        
        db = new DatabaseManager();
        await db.connect();
        console.log('  ✅ Database connected');
        
        dataProcessor = new DataProcessor(db);
        console.log('  ✅ Data processor ready');
        
        scraper = new DSVScraper('src/scrapers/config/events.json');
        await scraper.loadConfig();
        console.log('  ✅ DSV scraper initialized');
        
        // Step 2: Create test configuration
        console.log('\n🧪 Step 2: Creating test configuration...');
        
        const testConfig = new ScrapingConfig({
            eventName: '200m Lagen',
            eventCode: '200L|GL',
            distance: 200,
            stroke: 'Lagen',
            course: 'L',
            ageGroup: '2015|2015',
            gender: 'M',
            season: '2025',
            timeRange: '01.06.2024|31.05.2025',
            regionId: 15 // Schleswig-Holstein - reliable test region
        });
        
        console.log('  ✅ Test configuration created:');
        console.log(`     - Event: ${testConfig.eventName}`);
        console.log(`     - Age Group: ${testConfig.ageGroup}`);
        console.log(`     - Gender: ${testConfig.gender}`);
        console.log(`     - Region: ${testConfig.regionId} (Schleswig-Holstein)`);
        
        // Step 3: Test scraping
        console.log('\n🔍 Step 3: Testing scraping...');
        
        const results = await scraper.scrapeEvent(testConfig);
        console.log(`  📊 ${results.length} rankings scraped`);
        
        if (results.length === 0) {
            console.log('  ⚠️ No rankings found - cannot test database storage');
            return;
        }
        
        // Show sample results
        console.log('  📋 Sample rankings:');
        results.slice(0, 3).forEach((result, index) => {
            console.log(`    ${index + 1}. ${result.swimmerName} (${result.club}) - ${result.time}`);
        });
        
        // Step 4: Test database storage
        console.log('\n💾 Step 4: Testing database storage...');

        // Create or get event using upsert
        const eventCode = `TEST_${testConfig.distance}${testConfig.stroke.charAt(0)}|${testConfig.course}`;
        const eventName = `TEST ${testConfig.eventName}`;
        const eventData = {
            code: eventCode,
            name: eventName,
            distance: testConfig.distance,
            stroke: testConfig.stroke,
            course: testConfig.course
        };

        const event = await db.upsertEvent(eventData);
        console.log(`  ✅ Event created/found: ${event.id}`);
        
        // Process and store rankings directly
        const rankingsData = results.map(result => ({
            swimmerName: result.swimmerName,
            birthYear: result.birthYear,
            club: result.club,
            time: result.time,
            timeSeconds: result.timeSeconds,
            rank: result.rank,
            ageGroup: testConfig.ageGroup,
            gender: testConfig.gender,
            competition: result.competition,
            date: result.date,
            location: result.location,
            eventId: event.id,
            regionId: testConfig.regionId,
            season: testConfig.season
        }));

        const dbResult = await db.createManyRankings(rankingsData);
        console.log(`  ✅ ${rankingsData.length} rankings stored in database`);
        
        // Step 5: Verify database storage
        console.log('\n🔍 Step 5: Verifying database storage...');
        
        const storedRankings = await db.getRankings({
            eventId: event.id,
            regionId: testConfig.regionId,
            ageGroup: testConfig.ageGroup,
            gender: testConfig.gender,
            season: testConfig.season
        });
        
        console.log(`  📊 ${storedRankings.length} rankings found in database`);
        
        if (storedRankings.length === results.length) {
            console.log('  ✅ All rankings successfully stored!');
        } else {
            console.log(`  ⚠️ Mismatch: scraped ${results.length}, stored ${storedRankings.length}`);
        }
        
        // Show sample stored rankings
        console.log('  📋 Sample stored rankings:');
        storedRankings.slice(0, 3).forEach((ranking, index) => {
            console.log(`    ${index + 1}. ${ranking.swimmerName} (${ranking.club}) - ${ranking.time}`);
        });
        
        // Step 6: Test data retrieval
        console.log('\n📈 Step 6: Testing data retrieval...');

        // Get all rankings for this event
        const allEventRankings = await db.getRankings({ eventId: event.id });
        console.log(`  📊 Event statistics:`);
        console.log(`     - Total rankings: ${allEventRankings.length}`);

        // Get unique regions, age groups, genders
        const regions = [...new Set(allEventRankings.map(r => r.regionId))];
        const ageGroups = [...new Set(allEventRankings.map(r => r.ageGroup))];
        const genders = [...new Set(allEventRankings.map(r => r.gender))];

        console.log(`     - Regions: ${regions.length} (${regions.join(', ')})`);
        console.log(`     - Age groups: ${ageGroups.length} (${ageGroups.join(', ')})`);
        console.log(`     - Genders: ${genders.length} (${genders.join(', ')})`);
        
        // Step 7: Test cleanup (optional)
        console.log('\n🧹 Step 7: Testing cleanup...');
        
        const deletedCount = await db.deleteRankingsForEvent(
            event.id,
            testConfig.regionId,
            testConfig.ageGroup,
            testConfig.gender,
            testConfig.season
        );
        
        console.log(`  🗑️ ${deletedCount} rankings deleted from database`);
        
        // Verify cleanup
        const remainingRankings = await db.getRankings({
            eventId: event.id,
            regionId: testConfig.regionId,
            ageGroup: testConfig.ageGroup,
            gender: testConfig.gender,
            season: testConfig.season
        });
        
        if (remainingRankings.length === 0) {
            console.log('  ✅ Cleanup successful - no rankings remain');
        } else {
            console.log(`  ⚠️ Cleanup incomplete - ${remainingRankings.length} rankings remain`);
        }
        
        console.log('\n🎉 Test completed successfully!');
        console.log('=====================================');
        console.log('✅ Scraping pipeline is working correctly:');
        console.log('   - Data scraping from DSV website');
        console.log('   - Data processing and validation');
        console.log('   - Database storage and retrieval');
        console.log('   - Data cleanup and management');
        
    } catch (error) {
        console.error('\n❌ Test failed:', error.message);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    } finally {
        // Cleanup
        if (db) {
            await db.disconnect();
            console.log('\n🔌 Database disconnected');
        }
    }
}

// Handle process signals
process.on('SIGINT', () => {
    console.log('\n\n⏹️  Test interrupted by user');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n\n⏹️  Test terminated');
    process.exit(0);
});

// Run the test
console.log('Starting DSV Scraping + Database Test...\n');
testScrapingWithDatabase().catch(error => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
});

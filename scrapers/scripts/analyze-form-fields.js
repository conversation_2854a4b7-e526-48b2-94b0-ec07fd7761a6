#!/usr/bin/env node

/**
 * Analyze Form Fields Script
 * Analyzes all form fields to understand what's missing
 */

import axios from 'axios';
import * as cheerio from 'cheerio';

async function analyzeFormFields() {
    console.log('🔍 DSV Form Fields Analysis');
    console.log('============================');
    console.log('Analyzing all form fields to understand what might be missing...\n');
    
    try {
        // Step 1: Get initial page
        console.log('📥 Step 1: Getting initial page...');
        
        const url = 'https://dsvdaten.dsv.de/Modules/Clubs/Index.aspx?StateID=15';
        const getResponse = await axios.get(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'de-DE,de;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive'
            }
        });
        
        console.log(`  ✅ Page downloaded (${getResponse.data.length} bytes)`);
        
        // Step 2: Analyze all form fields
        console.log('\n🔍 Step 2: Analyzing all form fields...');
        
        const $ = cheerio.load(getResponse.data);
        
        // Find the main form
        const form = $('form').first();
        console.log(`  ✅ Found form with action: ${form.attr('action') || 'same page'}`);
        
        // Analyze all input fields
        console.log('\n📋 All Input Fields:');
        console.log('====================');
        
        const allInputs = $('input');
        console.log(`Total inputs: ${allInputs.length}\n`);
        
        const inputsByType = {};
        
        allInputs.each((i, element) => {
            const type = $(element).attr('type') || 'text';
            const id = $(element).attr('id') || '';
            const name = $(element).attr('name') || '';
            const value = $(element).attr('value') || '';
            
            if (!inputsByType[type]) {
                inputsByType[type] = [];
            }
            
            inputsByType[type].push({
                id,
                name,
                value: value.length > 50 ? `${value.substring(0, 50)}...` : value
            });
        });
        
        // Display by type
        Object.keys(inputsByType).forEach(type => {
            console.log(`${type.toUpperCase()} inputs (${inputsByType[type].length}):`);
            inputsByType[type].forEach(input => {
                console.log(`  - id="${input.id}", name="${input.name}", value="${input.value}"`);
            });
            console.log('');
        });
        
        // Analyze all select fields
        console.log('\n📋 All Select Fields:');
        console.log('=====================');
        
        const allSelects = $('select');
        console.log(`Total selects: ${allSelects.length}\n`);
        
        allSelects.each((i, element) => {
            const id = $(element).attr('id') || '';
            const name = $(element).attr('name') || '';
            const selectedValue = $(element).find('option:selected').attr('value') || '';
            const optionCount = $(element).find('option').length;
            
            console.log(`Select ${i + 1}:`);
            console.log(`  - id="${id}"`);
            console.log(`  - name="${name}"`);
            console.log(`  - selected="${selectedValue}"`);
            console.log(`  - options=${optionCount}`);
            console.log('');
        });
        
        // Step 3: Simulate form submission with ALL fields
        console.log('\n🔧 Step 3: Building complete form payload...');
        
        const completePayload = {};
        
        // Add all input fields
        $('input').each((i, element) => {
            const name = $(element).attr('name');
            const value = $(element).attr('value') || '';
            const type = $(element).attr('type') || 'text';
            
            if (name) {
                if (type === 'checkbox' || type === 'radio') {
                    // Only include if checked
                    if ($(element).prop('checked')) {
                        completePayload[name] = value;
                    }
                } else {
                    completePayload[name] = value;
                }
            }
        });
        
        // Add all select fields with their selected values
        $('select').each((i, element) => {
            const name = $(element).attr('name');
            const selectedValue = $(element).find('option:selected').attr('value') || '';
            
            if (name) {
                completePayload[name] = selectedValue;
            }
        });
        
        // Override with our specific values
        completePayload['ctl00$ContentSection$_eventDropDownList'] = '200L|GL';
        completePayload['ctl00$ContentSection$_ageDropDownList'] = '2015|2015';
        completePayload['ctl00$ContentSection$_timerangeDropDownList'] = '01.06.2024|31.05.2025';
        
        console.log('Complete payload fields:');
        Object.keys(completePayload).forEach(key => {
            const value = completePayload[key];
            const displayValue = value.length > 50 ? `${value.substring(0, 50)}...` : value;
            console.log(`  ${key}: ${displayValue}`);
        });
        
        // Step 4: Test first POST with complete payload
        console.log('\n📤 Step 4: Testing first POST with complete payload...');
        
        const postHeaders = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Referer': url,
            'Origin': 'https://dsvdaten.dsv.de',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'de-DE,de;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive'
        };
        
        const filterResponse = await axios.post(url, new URLSearchParams(completePayload).toString(), {
            headers: postHeaders
        });
        
        console.log(`  ✅ First POST successful (${filterResponse.status}, ${filterResponse.data.length} bytes)`);
        
        // Step 5: Extract updated fields and test button click
        console.log('\n🔧 Step 5: Extracting updated fields for button click...');
        
        const $filter = cheerio.load(filterResponse.data);
        const updatedPayload = {};
        
        // Extract all fields from the updated page
        $filter('input').each((i, element) => {
            const name = $filter(element).attr('name');
            const value = $filter(element).attr('value') || '';
            const type = $filter(element).attr('type') || 'text';
            
            if (name) {
                if (type === 'checkbox' || type === 'radio') {
                    if ($filter(element).prop('checked')) {
                        updatedPayload[name] = value;
                    }
                } else {
                    updatedPayload[name] = value;
                }
            }
        });
        
        $filter('select').each((i, element) => {
            const name = $filter(element).attr('name');
            const selectedValue = $filter(element).find('option:selected').attr('value') || '';
            
            if (name) {
                updatedPayload[name] = selectedValue;
            }
        });
        
        // Override with our specific values and add button
        updatedPayload['ctl00$ContentSection$_eventDropDownList'] = '200L|GL';
        updatedPayload['ctl00$ContentSection$_ageDropDownList'] = '2015|2015';
        updatedPayload['ctl00$ContentSection$_timerangeDropDownList'] = '01.06.2024|31.05.2025';
        updatedPayload['ctl00$ContentSection$_rankingsButton'] = 'Daten laden';
        
        console.log(`  ✅ Updated payload has ${Object.keys(updatedPayload).length} fields`);
        
        // Step 6: Test button click
        console.log('\n📤 Step 6: Testing button click with complete payload...');
        
        try {
            const dataResponse = await axios.post(url, new URLSearchParams(updatedPayload).toString(), {
                headers: postHeaders
            });
            
            console.log(`  ✅ Button click successful (${dataResponse.status}, ${dataResponse.data.length} bytes)`);
            
            // Check for rankings
            const $data = cheerio.load(dataResponse.data);
            const table = $data('#rankings .table-responsive table tbody tr');
            console.log(`  📊 Found ${table.length} ranking rows`);
            
            if (table.length > 0) {
                console.log('\n🎉 SUCCESS! Rankings found!');
                table.slice(0, 5).each((i, row) => {
                    const cols = $data(row).find('td');
                    if (cols.length >= 5) {
                        const rank = $data(cols[0]).text().trim();
                        const name = $data(cols[1]).text().trim();
                        const time = $data(cols[4]).text().trim();
                        console.log(`    ${rank}. ${name} - ${time}`);
                    }
                });
            } else {
                console.log('  ⚠️ No rankings found');
            }
            
        } catch (error) {
            console.log(`  ❌ Button click failed: ${error.message}`);
            console.log(`  Status: ${error.response?.status}`);
        }
        
    } catch (error) {
        console.error('\n❌ Analysis failed:', error.message);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    }
}

// Run the analysis
console.log('Starting DSV Form Fields Analysis...\n');
analyzeFormFields().catch(error => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
});

#!/usr/bin/env node

/**
 * Debug POST Data Script
 * Analyzes the exact POST data being sent to understand the 500 error
 */

import axios from 'axios';
import * as cheerio from 'cheerio';
import fs from 'fs/promises';

async function debugPostData() {
    console.log('🔍 DSV POST Data Debug');
    console.log('=======================');
    console.log('Analyzing the exact POST data being sent...\n');
    
    try {
        // Step 1: Get initial page
        console.log('📥 Step 1: Getting initial page...');
        
        const url = 'https://dsvdaten.dsv.de/Modules/Clubs/Index.aspx?StateID=15';
        const getResponse = await axios.get(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'de-DE,de;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive'
            }
        });
        
        console.log(`  ✅ Page downloaded (${getResponse.data.length} bytes)`);
        
        // Step 2: Extract hidden fields
        console.log('\n🔍 Step 2: Extracting hidden fields...');
        
        const $ = cheerio.load(getResponse.data);
        const hiddenFields = {};
        
        $('input[type="hidden"]').each((i, element) => {
            const name = $(element).attr('name');
            const value = $(element).attr('value') || '';
            if (name) {
                hiddenFields[name] = value;
            }
        });
        
        console.log(`  ✅ Found ${Object.keys(hiddenFields).length} hidden fields`);
        console.log('  Key hidden fields:');
        Object.keys(hiddenFields).forEach(key => {
            if (key.startsWith('__')) {
                const value = hiddenFields[key];
                console.log(`    ${key}: ${value.length} chars`);
            }
        });
        
        // Step 3: Build first POST payload (filter setting)
        console.log('\n🔧 Step 3: Building first POST payload...');
        
        const filterPayload = {
            ...hiddenFields,
            'ctl00$ContentSection$_eventDropDownList': '200L|GL',
            'ctl00$ContentSection$_ageDropDownList': '2015|2015',
            'ctl00$ContentSection$_timerangeDropDownList': '01.06.2024|31.05.2025'
        };
        
        console.log('  Filter payload keys:');
        Object.keys(filterPayload).forEach(key => {
            if (!key.startsWith('__')) {
                console.log(`    ${key}: ${filterPayload[key]}`);
            }
        });
        
        // Step 4: Send first POST request
        console.log('\n📤 Step 4: Sending first POST request...');
        
        const postHeaders = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Referer': url,
            'Origin': 'https://dsvdaten.dsv.de',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'de-DE,de;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive'
        };
        
        const filterResponse = await axios.post(url, new URLSearchParams(filterPayload).toString(), {
            headers: postHeaders
        });
        
        console.log(`  ✅ First POST successful (${filterResponse.status}, ${filterResponse.data.length} bytes)`);
        
        // Step 5: Extract updated hidden fields
        console.log('\n🔍 Step 5: Extracting updated hidden fields...');
        
        const $filter = cheerio.load(filterResponse.data);
        const hiddenFields2 = {};
        
        $filter('input[type="hidden"]').each((i, element) => {
            const name = $filter(element).attr('name');
            const value = $filter(element).attr('value') || '';
            if (name) {
                hiddenFields2[name] = value;
            }
        });
        
        console.log(`  ✅ Found ${Object.keys(hiddenFields2).length} updated hidden fields`);
        
        // Compare ViewState changes
        const viewStateChanged = hiddenFields['__VIEWSTATE'] !== hiddenFields2['__VIEWSTATE'];
        const eventValidationChanged = hiddenFields['__EVENTVALIDATION'] !== hiddenFields2['__EVENTVALIDATION'];
        
        console.log(`  ViewState changed: ${viewStateChanged ? '✅' : '❌'}`);
        console.log(`  EventValidation changed: ${eventValidationChanged ? '✅' : '❌'}`);
        
        // Step 6: Build second POST payload (button click)
        console.log('\n🔧 Step 6: Building second POST payload...');
        
        const dataPayload = {
            ...hiddenFields2,
            'ctl00$ContentSection$_eventDropDownList': '200L|GL',
            'ctl00$ContentSection$_ageDropDownList': '2015|2015',
            'ctl00$ContentSection$_timerangeDropDownList': '01.06.2024|31.05.2025',
            'ctl00$ContentSection$_rankingsButton': 'Daten laden'
        };
        
        console.log('  Data payload keys (non-hidden):');
        Object.keys(dataPayload).forEach(key => {
            if (!key.startsWith('__')) {
                console.log(`    ${key}: ${dataPayload[key]}`);
            }
        });
        
        // Step 7: Send second POST request
        console.log('\n📤 Step 7: Sending second POST request...');
        
        try {
            const dataResponse = await axios.post(url, new URLSearchParams(dataPayload).toString(), {
                headers: postHeaders
            });
            
            console.log(`  ✅ Second POST successful (${dataResponse.status}, ${dataResponse.data.length} bytes)`);
            
            // Check for rankings table
            const $data = cheerio.load(dataResponse.data);
            const table = $data('#rankings .table-responsive table tbody tr');
            console.log(`  📊 Found ${table.length} ranking rows`);
            
            if (table.length > 0) {
                console.log('  ✅ Rankings found! Scraping is working!');
                table.slice(0, 3).each((i, row) => {
                    const cols = $data(row).find('td');
                    if (cols.length >= 5) {
                        const rank = $data(cols[0]).text().trim();
                        const name = $data(cols[1]).text().trim();
                        const time = $data(cols[4]).text().trim();
                        console.log(`    ${rank}. ${name} - ${time}`);
                    }
                });
            } else {
                console.log('  ⚠️ No rankings found in response');
                
                // Save response for debugging
                await fs.writeFile('debug-response.html', dataResponse.data);
                console.log('  💾 Response saved to debug-response.html');
            }
            
        } catch (error) {
            console.log(`  ❌ Second POST failed: ${error.message}`);
            console.log(`  Status: ${error.response?.status}`);
            
            if (error.response?.data) {
                await fs.writeFile('debug-error-response.html', error.response.data);
                console.log('  💾 Error response saved to debug-error-response.html');
            }
            
            // Analyze the error
            if (error.response?.status === 500) {
                console.log('\n🔍 500 Error Analysis:');
                console.log('  Possible causes:');
                console.log('  - Missing required form fields');
                console.log('  - Invalid ViewState/EventValidation');
                console.log('  - Incorrect button value or name');
                console.log('  - Server-side validation failure');
            }
        }
        
        console.log('\n📊 Summary:');
        console.log('===========');
        console.log(`✅ GET request: Success`);
        console.log(`✅ First POST (filter): Success`);
        console.log(`${filterResponse.status === 200 ? '✅' : '❌'} Second POST (button): ${filterResponse.status === 200 ? 'Success' : 'Failed'}`);
        
    } catch (error) {
        console.error('\n❌ Debug failed:', error.message);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    }
}

// Run the debug
console.log('Starting DSV POST Data Debug...\n');
debugPostData().catch(error => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
});

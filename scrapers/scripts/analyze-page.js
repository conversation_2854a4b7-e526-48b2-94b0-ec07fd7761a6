#!/usr/bin/env node

/**
 * Analyze DSV Page Structure
 * Downloads and analyzes the actual HTML structure of the DSV website
 */

import axios from 'axios';
import * as cheerio from 'cheerio';
import fs from 'fs/promises';

async function analyzePage() {
    console.log('🔍 DSV Page Structure Analysis');
    console.log('==============================');
    console.log('Downloading and analyzing the actual HTML structure...\n');
    
    try {
        // Step 1: Download page
        console.log('📥 Step 1: Downloading page...');
        
        const url = 'https://dsvdaten.dsv.de/Modules/Clubs/Index.aspx?StateID=15';
        const response = await axios.get(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'de-DE,de;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive'
            }
        });
        
        console.log(`  ✅ Page downloaded (${response.data.length} bytes)`);
        
        // Step 2: Parse HTML
        console.log('\n🔍 Step 2: Analyzing HTML structure...');
        
        const $ = cheerio.load(response.data);
        
        // Save HTML for manual inspection
        await fs.writeFile('debug-page.html', response.data);
        console.log('  💾 HTML saved to debug-page.html');
        
        // Step 3: Look for form elements
        console.log('\n📋 Step 3: Looking for form elements...');
        
        // Check for dropdowns and inputs
        const eventDropdown = $('#ctl00_ContentSection__eventDropDown');
        const ageGroupDropdown = $('#ctl00_ContentSection__ageGroupDropDown');
        const genderDropdown = $('#ctl00_ContentSection__genderDropDown');
        const rankingsButton = $('#ctl00_ContentSection__rankingsButton');
        const timeRangeInput = $('#ctl00_ContentSection__timeRangeTextBox');
        const courseDropdown = $('#ctl00_ContentSection__courseDropDown');
        
        console.log('  Form elements found:');
        console.log(`    - Event Dropdown: ${eventDropdown.length > 0 ? '✅' : '❌'} (${eventDropdown.length})`);
        console.log(`    - Age Group Dropdown: ${ageGroupDropdown.length > 0 ? '✅' : '❌'} (${ageGroupDropdown.length})`);
        console.log(`    - Gender Dropdown: ${genderDropdown.length > 0 ? '✅' : '❌'} (${genderDropdown.length})`);
        console.log(`    - Rankings Button: ${rankingsButton.length > 0 ? '✅' : '❌'} (${rankingsButton.length})`);
        console.log(`    - Time Range Input: ${timeRangeInput.length > 0 ? '✅' : '❌'} (${timeRangeInput.length})`);
        console.log(`    - Course Dropdown: ${courseDropdown.length > 0 ? '✅' : '❌'} (${courseDropdown.length})`);
        
        // Step 4: Look for alternative selectors
        console.log('\n🔍 Step 4: Looking for alternative selectors...');
        
        // Look for any select elements
        const allSelects = $('select');
        console.log(`  Total select elements: ${allSelects.length}`);
        
        allSelects.each((i, element) => {
            const id = $(element).attr('id');
            const name = $(element).attr('name');
            const options = $(element).find('option').length;
            console.log(`    Select ${i + 1}: id="${id}", name="${name}", options=${options}`);
        });
        
        // Look for any input elements
        const allInputs = $('input');
        console.log(`\n  Total input elements: ${allInputs.length}`);
        
        allInputs.each((i, element) => {
            const type = $(element).attr('type');
            const id = $(element).attr('id');
            const name = $(element).attr('name');
            const value = $(element).attr('value');
            
            if (type === 'submit' || type === 'button' || id?.includes('ranking') || name?.includes('ranking')) {
                console.log(`    Input ${i + 1}: type="${type}", id="${id}", name="${name}", value="${value}"`);
            }
        });
        
        // Step 5: Look for hidden fields
        console.log('\n🔍 Step 5: Analyzing hidden fields...');
        
        const hiddenInputs = $('input[type="hidden"]');
        console.log(`  Hidden inputs found: ${hiddenInputs.length}`);
        
        let viewStateFound = false;
        let eventValidationFound = false;
        
        hiddenInputs.each((i, element) => {
            const name = $(element).attr('name');
            const value = $(element).attr('value');
            
            if (name === '__VIEWSTATE') {
                viewStateFound = true;
                console.log(`    ✅ __VIEWSTATE found (${value?.length || 0} chars)`);
            } else if (name === '__EVENTVALIDATION') {
                eventValidationFound = true;
                console.log(`    ✅ __EVENTVALIDATION found (${value?.length || 0} chars)`);
            } else if (name?.startsWith('__')) {
                console.log(`    📋 ${name}: ${value?.substring(0, 50)}...`);
            }
        });
        
        // Step 6: Look for content sections
        console.log('\n🔍 Step 6: Looking for content sections...');
        
        const contentSection = $('#ctl00_ContentSection');
        const contentSectionAlt = $('[id*="ContentSection"]');
        const mainContent = $('#MainContent');
        const form = $('form');
        
        console.log(`  Content sections found:`);
        console.log(`    - #ctl00_ContentSection: ${contentSection.length > 0 ? '✅' : '❌'} (${contentSection.length})`);
        console.log(`    - [id*="ContentSection"]: ${contentSectionAlt.length > 0 ? '✅' : '❌'} (${contentSectionAlt.length})`);
        console.log(`    - #MainContent: ${mainContent.length > 0 ? '✅' : '❌'} (${mainContent.length})`);
        console.log(`    - form: ${form.length > 0 ? '✅' : '❌'} (${form.length})`);
        
        // Step 7: Look for any elements with "ranking" in their attributes
        console.log('\n🔍 Step 7: Looking for ranking-related elements...');
        
        const rankingElements = $('[id*="ranking"], [name*="ranking"], [class*="ranking"]');
        console.log(`  Ranking-related elements: ${rankingElements.length}`);
        
        rankingElements.each((i, element) => {
            const tagName = element.tagName;
            const id = $(element).attr('id');
            const name = $(element).attr('name');
            const className = $(element).attr('class');
            console.log(`    ${tagName}: id="${id}", name="${name}", class="${className}"`);
        });
        
        // Step 8: Summary
        console.log('\n📊 Summary:');
        console.log('===========');
        
        if (viewStateFound && eventValidationFound) {
            console.log('✅ ASP.NET ViewState structure detected');
        } else {
            console.log('❌ ASP.NET ViewState structure missing');
        }
        
        if (allSelects.length > 0) {
            console.log('✅ Form dropdowns found');
        } else {
            console.log('❌ No form dropdowns found');
        }
        
        console.log('\n💡 Next steps:');
        console.log('   1. Check debug-page.html for manual inspection');
        console.log('   2. Update selectors based on actual HTML structure');
        console.log('   3. Verify the correct form field names and IDs');
        
    } catch (error) {
        console.error('\n❌ Analysis failed:', error.message);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    }
}

// Run the analysis
console.log('Starting DSV Page Analysis...\n');
analyzePage().catch(error => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
});

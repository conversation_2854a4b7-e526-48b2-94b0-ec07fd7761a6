#!/bin/bash

# Test Script für DSV Scraper - Node.js Version

set -e

echo "🧪 DSV Scraper Test Suite"
echo "========================="

# Farben für Output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funktionen
print_step() {
    echo -e "${BLUE}📋 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Prüfe Node.js Version
print_step "Prüfe Node.js Version..."
NODE_VERSION=$(node --version)
echo "Node.js Version: $NODE_VERSION"

if [[ "$NODE_VERSION" < "v18.0.0" ]]; then
    print_error "Node.js 18.0.0+ er<PERSON><PERSON><PERSON>"
    exit 1
fi

print_success "Node.js Version OK"

# Prüfe npm Version
print_step "Prüfe npm Version..."
NPM_VERSION=$(npm --version)
echo "npm Version: $NPM_VERSION"
print_success "npm Version OK"

# Installiere Dependencies falls nötig
if [ ! -d "node_modules" ]; then
    print_step "Installiere Dependencies..."
    npm install
    print_success "Dependencies installiert"
fi

# Prüfe Environment
print_step "Prüfe Test Environment..."
export NODE_ENV=test
export DATABASE_TYPE=sqlite
export DATABASE_URL=file:./test.db
export LOG_LEVEL=error
export ENABLE_SCHEDULER=false

print_success "Test Environment konfiguriert"

# Bereinige alte Test-Dateien
print_step "Bereinige alte Test-Dateien..."
rm -f test.db
rm -rf coverage/
print_success "Test-Dateien bereinigt"

# Linting
print_step "Führe Linting durch..."
if npm run lint; then
    print_success "Linting erfolgreich"
else
    print_warning "Linting Warnungen gefunden"
fi

# Unit Tests
print_step "Führe Unit Tests durch..."
if npm test -- --testPathPattern="tests/(config|utils|scrapers|scheduler)" --verbose; then
    print_success "Unit Tests erfolgreich"
else
    print_error "Unit Tests fehlgeschlagen"
    exit 1
fi

# Integration Tests
print_step "Führe Integration Tests durch..."
if npm test -- --testPathPattern="tests/integration" --verbose; then
    print_success "Integration Tests erfolgreich"
else
    print_error "Integration Tests fehlgeschlagen"
    exit 1
fi

# Web Application Tests
print_step "Führe Web Application Tests durch..."
if npm test -- --testPathPattern="tests/web" --verbose; then
    print_success "Web Application Tests erfolgreich"
else
    print_error "Web Application Tests fehlgeschlagen"
    exit 1
fi

# Coverage Report
print_step "Generiere Coverage Report..."
if npm test -- --coverage --coverageReporters=text-summary; then
    print_success "Coverage Report generiert"
else
    print_warning "Coverage Report konnte nicht generiert werden"
fi

# Performance Tests (optional)
if [ "$1" = "--performance" ]; then
    print_step "Führe Performance Tests durch..."
    
    # Einfacher Performance Test
    echo "Teste Scraper Performance..."
    time node -e "
        import('./src/scrapers/DSVScraper.js').then(module => {
            const scraper = new module.DSVScraper('src/scrapers/config/events.json');
            console.log('Scraper erstellt in:', process.hrtime());
        });
    "
    
    print_success "Performance Tests abgeschlossen"
fi

# Security Audit
print_step "Führe Security Audit durch..."
if npm audit --audit-level=moderate; then
    print_success "Security Audit erfolgreich"
else
    print_warning "Security Vulnerabilities gefunden"
fi

# Bereinigung
print_step "Bereinige Test-Artefakte..."
rm -f test.db
print_success "Test-Artefakte bereinigt"

# Zusammenfassung
echo ""
echo "🎉 Test Suite abgeschlossen!"
echo "============================="

# Zeige Coverage Summary falls vorhanden
if [ -f "coverage/coverage-summary.json" ]; then
    echo ""
    echo "📊 Coverage Summary:"
    node -e "
        const fs = require('fs');
        const coverage = JSON.parse(fs.readFileSync('coverage/coverage-summary.json'));
        const total = coverage.total;
        console.log('Lines:', total.lines.pct + '%');
        console.log('Functions:', total.functions.pct + '%');
        console.log('Branches:', total.branches.pct + '%');
        console.log('Statements:', total.statements.pct + '%');
    "
fi

echo ""
print_success "Alle Tests erfolgreich! 🚀"

# Exit Code
exit 0

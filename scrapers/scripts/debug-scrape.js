#!/usr/bin/env node

/**
 * Debug Scraping Script for DSV Rankings
 * Tests scraping with a single region to debug issues
 */

import { DSVScraper } from '../src/scrapers/DSVScraper.js';
import { ScrapingConfig } from '../src/scrapers/BaseScraper.js';
import { scraperLogger } from '../src/utils/logger.js';

async function debugScrape() {
    console.log('🔍 DSV Debug Scraping');
    console.log('=====================');
    console.log('Testing scraping with a single region to debug issues...\n');
    
    let scraper;
    
    try {
        // Step 1: Initialize scraper
        console.log('📦 Step 1: Initializing scraper...');
        
        scraper = new DSVScraper('src/scrapers/config/events.json');
        await scraper.loadConfig();
        console.log('  ✅ DSV scraper initialized');
        
        // Step 2: Create test configuration
        console.log('\n🧪 Step 2: Creating test configuration...');
        
        const testConfig = new ScrapingConfig({
            eventName: '200m Lagen',
            eventCode: '200L|GL',
            distance: 200,
            stroke: 'Lagen',
            course: 'L',
            ageGroup: '2015|2015', // Korrigiertes Format
            gender: 'M',
            season: '2025',
            timeRange: '01.06.2024|31.05.2025',
            regionId: 15 // Schleswig-Holstein - usually has data
        });
        
        console.log('  ✅ Test configuration created:');
        console.log(`     - Event: ${testConfig.eventName}`);
        console.log(`     - Age Group: ${testConfig.ageGroup}`);
        console.log(`     - Gender: ${testConfig.gender}`);
        console.log(`     - Region: ${testConfig.regionId} (Schleswig-Holstein)`);
        
        // Step 3: Test scraping
        console.log('\n🔍 Step 3: Testing scraping...');
        console.log('This will help us identify the exact issue...\n');
        
        const results = await scraper.scrapeEvent(testConfig);
        
        console.log(`\n📈 Step 4: Scraping completed!`);
        console.log(`  📊 ${results.length} rankings found`);
        
        if (results.length > 0) {
            console.log('\n✅ Success! Found rankings:');
            results.slice(0, 5).forEach((result, index) => {
                console.log(`  ${index + 1}. ${result.swimmerName} (${result.club}) - ${result.time}`);
            });
            
            if (results.length > 5) {
                console.log(`  ... and ${results.length - 5} more`);
            }
        } else {
            console.log('\n⚠️  No rankings found. Possible reasons:');
            console.log('   - The event/age group combination has no data');
            console.log('   - The DSV website structure has changed');
            console.log('   - The request parameters are incorrect');
            console.log('   - The website is temporarily unavailable');
        }
        
        // Step 4: Test with different parameters
        console.log('\n🔄 Step 5: Testing with different age group...');
        
        const testConfig2 = new ScrapingConfig({
            eventName: '200m Lagen',
            eventCode: '200L|GL',
            distance: 200,
            stroke: 'Lagen',
            course: 'L',
            ageGroup: '2012|2012', // Korrigiertes Format - Older age group - more likely to have data
            gender: 'M',
            season: '2025',
            timeRange: '01.06.2024|31.05.2025',
            regionId: 15 // Schleswig-Holstein
        });
        
        const results2 = await scraper.scrapeEvent(testConfig2);
        console.log(`  📊 ${results2.length} rankings found for age group 2012`);
        
        if (results2.length > 0) {
            console.log('  ✅ Success with age group 2012!');
            results2.slice(0, 3).forEach((result, index) => {
                console.log(`    ${index + 1}. ${result.swimmerName} - ${result.time}`);
            });
        }
        
        // Step 5: Test with Bayern (larger region)
        console.log('\n🔄 Step 6: Testing with Bayern (larger region)...');
        
        const testConfig3 = new ScrapingConfig({
            eventName: '200m Lagen',
            eventCode: '200L|GL',
            distance: 200,
            stroke: 'Lagen',
            course: 'L',
            ageGroup: '2012|2012', // Korrigiertes Format
            gender: 'M',
            season: '2025',
            timeRange: '01.06.2024|31.05.2025',
            regionId: 2 // Bayern - large region with lots of data
        });
        
        const results3 = await scraper.scrapeEvent(testConfig3);
        console.log(`  📊 ${results3.length} rankings found for Bayern`);
        
        if (results3.length > 0) {
            console.log('  ✅ Success with Bayern!');
            results3.slice(0, 3).forEach((result, index) => {
                console.log(`    ${index + 1}. ${result.swimmerName} - ${result.time}`);
            });
        }
        
        console.log('\n🎉 Debug scraping completed!');
        console.log('=====================================');
        
        const totalResults = results.length + results2.length + results3.length;
        if (totalResults > 0) {
            console.log('✅ Scraping is working! The issue might be:');
            console.log('   - Specific age groups have no data');
            console.log('   - Some regions have no data for the current season');
            console.log('   - The initial scrape configuration needs adjustment');
        } else {
            console.log('❌ No data found in any test. The issue might be:');
            console.log('   - DSV website structure has changed');
            console.log('   - Request parameters are incorrect');
            console.log('   - Website is blocking our requests');
        }
        
    } catch (error) {
        console.error('\n❌ Debug scraping failed:', error.message);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    }
}

// Handle process signals
process.on('SIGINT', () => {
    console.log('\n\n⏹️  Debug scraping interrupted by user');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n\n⏹️  Debug scraping terminated');
    process.exit(0);
});

// Run the debug scraping
console.log('Starting DSV Debug Scraping...\n');
debugScrape().catch(error => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
});

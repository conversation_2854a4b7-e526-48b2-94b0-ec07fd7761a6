{"events": [{"name": "200m Lagen", "code": "200L|GL", "distance": 200, "stroke": "Lagen", "course": "L", "enabled": true, "priority": 1}, {"name": "100m Freistil", "code": "100F|GL", "distance": 100, "stroke": "Freistil", "course": "L", "enabled": false, "priority": 2}, {"name": "50m Freistil", "code": "50F|GL", "distance": 50, "stroke": "Freistil", "course": "L", "enabled": false, "priority": 3}, {"name": "200m Freistil", "code": "200F|GL", "distance": 200, "stroke": "Freistil", "course": "L", "enabled": false, "priority": 4}, {"name": "400m Freistil", "code": "400F|GL", "distance": 400, "stroke": "Freistil", "course": "L", "enabled": false, "priority": 5}, {"name": "100m Brust", "code": "100B|GL", "distance": 100, "stroke": "Brust", "course": "L", "enabled": false, "priority": 6}, {"name": "100m Rücken", "code": "100R|GL", "distance": 100, "stroke": "<PERSON><PERSON><PERSON>", "course": "L", "enabled": false, "priority": 7}, {"name": "100m Schmetterling", "code": "100S|GL", "distance": 100, "stroke": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "course": "L", "enabled": false, "priority": 8}], "age_groups": [{"year": "2015", "name": "Jahrgang 2015", "enabled": true}, {"year": "2014", "name": "Jahrgang 2014", "enabled": false}, {"year": "2013", "name": "Jahrgang 2013", "enabled": false}, {"year": "2012", "name": "Jahrgang 2012", "enabled": false}, {"year": "2011", "name": "Jahrgang 2011", "enabled": false}, {"year": "2010", "name": "Jahrgang 2010", "enabled": false}], "genders": [{"code": "M", "name": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true}, {"code": "W", "name": "<PERSON><PERSON><PERSON>", "enabled": false}], "courses": [{"code": "L", "name": "Langbahn (50m)", "enabled": true}, {"code": "K", "name": "Kurzbahn (25m)", "enabled": false}], "seasons": [{"year": "2026", "name": "Saison 2025/2025", "enabled": false}, {"year": "2025", "name": "Saison 2024/2025", "enabled": true}, {"year": "2024", "name": "Saison 2023/2024", "enabled": false}], "time_ranges": [{"code": "01.08.2024|31.07.2025", "name": "Saison 2024/2025", "start_date": "2024-08-01", "end_date": "2025-07-31", "enabled": true}], "regions": [{"id": 1, "name": "Baden", "abbreviation": "BAD"}, {"id": 2, "name": "Bayern", "abbreviation": "BAY"}, {"id": 3, "name": "Berlin", "abbreviation": "B"}, {"id": 4, "name": "Brandenburg", "abbreviation": "BRA"}, {"id": 5, "name": "Bremen", "abbreviation": "BRE"}, {"id": 6, "name": "Hamburg", "abbreviation": "HAM"}, {"id": 7, "name": "Hessen", "abbreviation": "HE"}, {"id": 8, "name": "Mecklenburg-Vorpommern", "abbreviation": "MV"}, {"id": 9, "name": "Niedersachsen", "abbreviation": "NDS"}, {"id": 10, "name": "Rheinland", "abbreviation": "RLP"}, {"id": 11, "name": "Saarland", "abbreviation": "SAAR"}, {"id": 12, "name": "Sachsen", "abbreviation": "SAC"}, {"id": 13, "name": "Sachsen-Anhalt", "abbreviation": "SAH"}, {"id": 14, "name": "Schleswig-Holstein", "abbreviation": "SH"}, {"id": 15, "name": "Südwest", "abbreviation": "SW"}, {"id": 16, "name": "T<PERSON><PERSON><PERSON>en", "abbreviation": "THÜ"}, {"id": 17, "name": "Nordrhein-Westfalen", "abbreviation": "NRW"}, {"id": 18, "name": "Württemberg", "abbreviation": "WÜR"}], "scraping_config": {"base_url": "https://dsvdaten.dsv.de/Modules/Clubs/Index.aspx", "delay_between_requests": 1, "max_retries": 3, "timeout": 30, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"}}